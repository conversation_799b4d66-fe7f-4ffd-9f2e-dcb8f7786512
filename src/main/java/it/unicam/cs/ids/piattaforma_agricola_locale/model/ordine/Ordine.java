package it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine;


import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.stateOrdine.IStatoOrdine;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.ordine.stateOrdine.StatoOrdineNuovoInAttesaDiPagamento;
import it.unicam.cs.ids.piattaforma_agricola_locale.model.utenti.Acquirente;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class Ordine {
    private Long idOrdine;
    private Date dataOrdine;
    private double importoTotale;
    private Acquirente acquirente;
    private List<RigaOrdine> righeOrdine;
    private IStatoOrdine stato;

    public Ordine( Date dataOrdine, Acquirente acquirente) {

        this.dataOrdine = dataOrdine;
        this.importoTotale = 0.0;
        this.acquirente = acquirente;
        this.righeOrdine = new ArrayList<>();
        this.stato = new StatoOrdineNuovoInAttesaDiPagamento();
    }

    public Long getIdOrdine() {
        return idOrdine;
    }

    public void setIdOrdine(Long idOrdine) {
        this.idOrdine = idOrdine;
    }

    public double getImportoTotale() {
        return importoTotale;
    }

    public void setImportoTotale(double importoTotale) {
        this.importoTotale = importoTotale;
    }

    public Date getDataOrdine() {
        return dataOrdine;
    }

    public void setDataOrdine(Date dataOrdine) {
        this.dataOrdine = dataOrdine;
    }

    public Acquirente getAcquirente() {
        return acquirente;
    }

    public void setAcquirente(Acquirente acquirente) {
        this.acquirente = acquirente;
    }

    /**
     * Restituisce la rappresentazione descrittiva dello stato corrente
     * @return l'enum StatoCorrente corrispondente allo stato attuale
     */
    public StatoCorrente getStatoOrdine() {
        return stato.getStatoCorrente();
    }

    /**
     * Restituisce l'oggetto stato corrente (per il pattern State)
     * @return l'istanza di IStatoOrdine che rappresenta lo stato corrente
     */
    public IStatoOrdine getStato() {
        return stato;
    }

    /**
     * Imposta un nuovo stato per l'ordine (per il pattern State)
     * @param nuovoStato il nuovo stato da impostare
     */
    public void setStato(IStatoOrdine nuovoStato) {
        this.stato = nuovoStato;
    }

    public List<RigaOrdine> getRigheOrdine() {
        return righeOrdine;
    }

    public void setRigheOrdine(List<RigaOrdine> righeOrdine) {
        this.righeOrdine = righeOrdine;
    }

    // Metodi per gestire lo stato dell'ordine (delegano al pattern State)
    public void processa() {
        stato.processaOrdine(this);
    }

    public void spedisci() {
        stato.spedisciOrdine(this);
    }

    public void annulla() {
        stato.annullaOrdine(this);
    }

    public void consegna() {
        stato.consegnaOrdine(this);
    }

    /**
     * Metodo per effettuare il pagamento dell'ordine
     */
    public void paga() {
        // Questo metodo potrebbe essere aggiunto per gestire il pagamento
        // In questo caso, la transizione da "in attesa di pagamento" a "pagato"
        // sarà gestita tramite il metodo processa() che nel caso dello stato
        // "in attesa di pagamento" dovrebbe gestire il pagamento
        processa();
    }

}
