# Diagramma Architetturale della Piattaforma Agricola Locale - Iterazione 4 (Versione Ottimizzata)

Questo diagramma rappresenta l'architettura a layer della piattaforma agricola locale con **connessioni visive rafforzate** e **flussi di dipendenza chiari**. L'architettura segue il pattern a layer tipico delle applicazioni enterprise, con una chiara separazione delle responsabilità e dipendenze unidirezionali dall'alto verso il basso.

## Struttura Architetturale e Flusso dei Dati

```
┌─────────────────────────────────────────────────────────────┐
│                    CONTROLLER LAYER                        │ ← Entry Point
├─────────────────────────────────────────────────────────────┤
│                     SERVICE LAYER                          │ ← Business Logic
│  • Orchestrazione processi di business                     │
│  • Implementazione regole di dominio                       │
│  • Coordinamento tra repository                            │
├─────────────────────────────────────────────────────────────┤
│                   REPOSITORY LAYER                         │ ← Data Access
│  • Astrazione accesso ai dati                             │
│  • Operazioni CRUD sulle entità                           │
│  • Isolamento della persistenza                           │
├─────────────────────────────────────────────────────────────┤
│                     MODEL LAYER                            │ ← Domain Entities
│  • Entità del dominio                                     │
│  • Value Objects                                          │
│  • Business Logic intrinseca                              │
├─────────────────────────────────────────────────────────────┤
│                   COMMON LAYER                             │ ← Cross-cutting
│  • Pattern comportamentali                                │
│  • Interfacce e contratti                                 │
│  • Enumerazioni e utility                                 │
└─────────────────────────────────────────────────────────────┘
```

**Legenda dei Flussi:**

- 🔽 **Dipendenze**: Service → Repository → Model
- 🔄 **Collaborazioni**: Service ↔ Service (orchestrazione)
- 📡 **Notifiche**: Observer Pattern (Service → Observer)
- 🔀 **Trasformazioni**: Model → DTO (mapping)

```plantuml
@startuml

!theme plain
skinparam backgroundColor #FFFFFF
skinparam classAttributeIconSize 0
skinparam defaultFontName Arial
skinparam linetype ortho
skinparam nodesep 20
skinparam ranksep 30

' === DEFINIZIONI COLORI E STILI PER LAYER ===
skinparam package {
    BackgroundColor<<service>> #E8F5E8
    BorderColor<<service>> #2E7D32
    FontColor<<service>> #1B5E20
    FontStyle<<service>> bold

    BackgroundColor<<repository>> #FFF3E0
    BorderColor<<repository>> #E65100
    FontColor<<repository>> #BF360C
    FontStyle<<repository>> bold

    BackgroundColor<<model>> #F3E5F5
    BorderColor<<model>> #6A1B9A
    FontColor<<model>> #4A148C
    FontStyle<<model>> bold

    BackgroundColor<<dto>> #E1F5FE
    BorderColor<<dto>> #0277BD
    FontColor<<dto>> #01579B
    FontStyle<<dto>> bold

    BackgroundColor<<common>> #FAFAFA
    BorderColor<<common>> #424242
    FontColor<<common>> #212121
    FontStyle<<common>> bold
}

skinparam class {
    BackgroundColor<<service>> #E8F5E8
    BorderColor<<service>> #2E7D32
    FontColor<<service>> #1B5E20

    BackgroundColor<<repository>> #FFF3E0
    BorderColor<<repository>> #E65100
    FontColor<<repository>> #BF360C

    BackgroundColor<<model>> #F3E5F5
    BorderColor<<model>> #6A1B9A
    FontColor<<model>> #4A148C

    BackgroundColor<<dto>> #E1F5FE
    BorderColor<<dto>> #0277BD
    FontColor<<dto>> #01579B

    BackgroundColor<<pattern>> #FAFAFA
    BorderColor<<pattern>> #424242
    FontColor<<pattern>> #212121
}

' === STILI PER LE RELAZIONI ===
skinparam arrow {
    Color #333333
    Thickness 2
}

' Stili specifici per tipi di relazione
skinparam arrow<<dependency>> {
    Color #FF5722
    Thickness 3
}

skinparam arrow<<collaboration>> {
    Color #2196F3
    Thickness 2
}

skinparam arrow<<notification>> {
    Color #9C27B0
    Thickness 2
}

skinparam arrow<<inheritance>> {
    Color #4CAF50
    Thickness 2
}

' === ARCHITETTURA A LAYER CON FLUSSI CHIARI ===

' === LAYER 1: SERVICE (Business Logic Layer) ===
package "🔧 SERVICE LAYER" <<service>> {
    note top : "Business Logic - Orchestrazione e coordinamento delle operazioni di dominio\n🔽 Dipende da: Repository Layer\n🔄 Collabora con: Altri Service"

    package "Core Business Services" <<service>> {
        class ProdottoService <<service>> {
            -prodottoRepository: IProdottoRepository
            -certificazioneService: CertificazioneService
            -observers: List<ICuratoreObserver>
            --
            +creaProdotto(...): Prodotto
            +aggiornaProdotto(prodotto: Prodotto): Prodotto
            +mostraTuttiIProdotti(): List<Prodotto>
            +notificaObservers(prodotto: Prodotto): void
        }

        class OrdineService <<service>> {
            -ordineRepository: IOrdineRepository
            -rigaOrdineRepository: IRigaOrdineRepository
            -carrelloService: CarrelloService
            -observers: List<IVenditoreObserver>
            --
            +creaOrdineDaCarrello(acquirente: Acquirente): Ordine
            +confermaOrdine(ordine: Ordine): void
            +cambiaStatoOrdine(ordine: Ordine): void
            +pagaOrdine(ordine: Ordine, metodoPagamento: IMetodoPagamentoStrategy): boolean
        }

        class CarrelloService <<service>> {
            -carrelloRepository: ICarrelloRepository
            --
            +creaNuovoCarrello(acquirente: Acquirente): Carrello
            +aggiungiAlCarrello(acquirente: Acquirente, acquistabile: Acquistabile, quantita: int): void
            +ottieniCarrello(acquirente: Acquirente): Optional<Carrello>
            +svuotaCarrello(acquirente: Acquirente): void
        }

        class PacchettoService <<service>> {
            -pacchettoRepository: IPacchettoRepository
            -venditoreRepository: IVenditoreRepository
            --
            +creaPacchetto(...): Pacchetto
            +aggiornaPacchetto(pacchetto: Pacchetto): Pacchetto
            +aggiungiElementoAPacchetto(...): void
        }
    }

    package "User Management Services" <<service>> {
        class VenditoreService <<service>> {
            -venditoreRepository: IVenditoreRepository
            -certificazioneService: CertificazioneService
            -datiAziendaRepository: IDatiAziendaRepository
            --
            +aggiornaDatiAzienda(venditore: Venditore, datiAggiornati: DatiAzienda): void
            +aggiungiDatiAzienda(...): DatiAzienda
            +aggiungiCertificazioneAzienda(...): void
        }

        class CuratoreService <<service>> {
            -venditoreRepository: IVenditoreRepository
            -prodottoRepository: IProdottoRepository
            -datiAziendaRepository: IDatiAziendaRepository
            --
            +verificaProdotto(prodotto: Prodotto, approvato: boolean, feedback: String): void
            +verificaDatiAzienda(datiAzienda: DatiAzienda, approvato: boolean): void
            +ottieniProdottiInAttesaDiRevisione(): List<Prodotto>
        }

        class GestoreService <<service>> {
            -utenteBaseRepository: IUtenteBaseRepository
            -venditoreRepository: IVenditoreRepository
            --
            +getVenditoriInAttesaDiAccreditamento(): List<Venditore>
            +aggiornaStatoAccreditamentoVenditore(...): boolean
            +attivaDisattivaAcquirente(...): boolean
        }
    }

    package "Specialized Services" <<service>> {
        class EventoService <<service>> {
            -eventoRepository: IEventoRepository
            --
            +creaEvento(...): void
            +aggiornaEvento(...): void
            +aggiungiAziendaPartecipante(...): void
        }

        class ProcessoTrasformazioneService <<service>> {
            -processoRepository: IProcessoTrasformazioneRepository
            --
            +creaProcesso(...): ProcessoTrasformazione
            +aggiungiFaseAlProcesso(...): ProcessoTrasformazione
            +ottieniProcessiPerTrasformatore(...): List<ProcessoTrasformazione>
        }

        class CertificazioneService <<service>> {
            -certificazioneRepository: ICertificazioneRepository
            --
            +creaCertificazione(...): Certificazione
            +verificaCertificazione(certificazione: Certificazione): boolean
            +getAllCertificazioni(): List<Certificazione>
        }
    }

    package "Observer & Cross-cutting Services" <<service>> {
        class CuratoreObserverService <<service>> {
            -curatoreService: CuratoreService
            --
            +notificaNuovoProdotto(prodotto: Prodotto): void
        }

        class VenditoreObserverService <<service>> {
            -prodottoService: ProdottoService
            -pacchettoService: PacchettoService
            --
            +notificaNuovoOrdine(ordine: Ordine): void
        }

        class ProcessoMapper <<service>> {
            --
            +toDto(processo: ProcessoTrasformazione): ProcessoTrasformazioneDTO
        }
    }
}

' === LAYER 2: REPOSITORY (Data Access Layer) ===
package "💾 REPOSITORY LAYER" <<repository>> {
    note top : "Data Access - Astrazione della persistenza e operazioni CRUD\n🔽 Dipende da: Model Layer\n🔼 Utilizzato da: Service Layer"

    package "Core Domain Repositories" <<repository>> {
        interface IProdottoRepository <<repository>> {
            +save(prodotto: Prodotto): void
            +findById(id: Long): Prodotto
            +findByVenditore(venditore: Venditore): List<Prodotto>
            +mostraTuttiIProdotti(): List<Prodotto>
            +deleteById(id: Long): void
        }

        interface IOrdineRepository <<repository>> {
            +save(ordine: Ordine): void
            +findById(idOrdine: Long): Optional<Ordine>
            +findByAcquirente(acquirente: Acquirente): List<Ordine>
            +findByStato(stato: StatoCorrente): List<Ordine>
            +update(ordine: Ordine): void
        }

        interface IRigaOrdineRepository <<repository>> {
            +save(rigaOrdine: RigaOrdine): void
            +findById(idRiga: Long): Optional<RigaOrdine>
            +findByOrdine(ordine: Ordine): List<RigaOrdine>
            +deleteByOrdine(ordine: Ordine): void
        }

        interface ICarrelloRepository <<repository>> {
            +save(carrello: Carrello): void
            +findByAcquirente(acquirente: Acquirente): Optional<Carrello>
            +deleteByAcquirente(acquirente: Acquirente): void
        }

        interface IPacchettoRepository <<repository>> {
            +save(pacchetto: Pacchetto): void
            +findById(id: Long): Pacchetto
            +findByVenditore(venditore: Venditore): List<Pacchetto>
        }
    }

    package "User Management Repositories" <<repository>> {
        interface IVenditoreRepository <<repository>> {
            +save(venditore: Venditore): void
            +findById(id: Long): Optional<Venditore>
            +findByStatoAccreditamento(stato: StatoAccreditamento): List<Venditore>
        }

        interface ICuratoreRepository <<repository>> {
            +save(curatore: Curatore): void
            +findByStatoAccreditamento(stato: StatoAccreditamento): List<Curatore>
        }

        interface IAnimatoreRepository <<repository>> {
            +save(animatore: AnimatoreDellaFiliera): void
            +findByStatoAccreditamento(stato: StatoAccreditamento): List<AnimatoreDellaFiliera>
        }

        interface IUtenteBaseRepository <<repository>> {
            +save(utente: Utente): void
            +findById(id: Long): Optional<Utente>
            +findByEmail(email: String): Optional<Utente>
        }

        interface IDatiAziendaRepository <<repository>> {
            +save(datiAzienda: DatiAzienda): void
            +findByPartitaIva(partitaIva: String): Optional<DatiAzienda>
        }
    }

    package "Specialized Repositories" <<repository>> {
        interface ICertificazioneRepository <<repository>> {
            +save(certificazione: Certificazione): void
            +findById(id: Long): Optional<Certificazione>
            +findAll(): List<Certificazione>
        }

        interface IProcessoTrasformazioneRepository <<repository>> {
            +save(processo: ProcessoTrasformazione): void
            +findByTrasformatore(trasformatore: Trasformatore): List<ProcessoTrasformazione>
        }

        interface IEventoRepository <<repository>> {
            +save(evento: Evento): void
            +findByAnimatoreId(organizzatore: AnimatoreDellaFiliera): List<Evento>
            +mostraTuttiEventi(): List<Evento>
        }

        interface IMetodoDiColtivazioneRepository <<repository>> {
            +save(metodo: MetodoDiColtivazione): void
            +findById(id: Long): Optional<MetodoDiColtivazione>
        }
    }
}

' === LAYER 3: MODEL (Domain Layer) ===
package "🏗️ DOMAIN MODEL LAYER" <<model>> {
    note top : "Domain Entities - Entità del dominio, Value Objects e Business Logic\n🔼 Utilizzato da: Repository Layer\n🔄 Implementa: Common Layer interfaces"

    package "👥 User Domain" <<model>> {
        abstract class Utente <<model>> {
            #idUtente: Long
            #nome: String
            #cognome: String
            #email: String
            #passwordHash: String
            #numeroTelefono: String
            #tipoRuolo: TipoRuolo
            #isAttivo: boolean
            --
            +getId(): Long
            +getNome(): String
            +getEmail(): String
        }

        class Acquirente <<model>> {
            +Acquirente(...)
        }

        abstract class Venditore <<model>> {
            #datiAzienda: DatiAzienda
            #prodottiOfferti: List<Prodotto>
            #statoAccreditamento: StatoAccreditamento
            --
            +getDatiAzienda(): DatiAzienda
            +getProdottiOfferti(): List<Prodotto>
        }

        class Produttore <<model>> {
            +Produttore(...)
        }

        class DistributoreDiTipicita <<model>> {
            +DistributoreDiTipicita(...)
        }

        class Trasformatore <<model>> {
            -metodiDiTrasformazione: List<String>
            --
            +getMetodiDiTrasformazione(): List<String>
        }

        class Curatore <<model>> {
            -specializzazione: String
            -annoAbilitazione: int
            -statoAccreditamento: StatoAccreditamento
        }

        class AnimatoreDellaFiliera <<model>> {
            -areaCompetenza: String
            -esperienza: int
            -statoAccreditamento: StatoAccreditamento
        }

        class GestorePiattaforma <<model>> {
            +GestorePiattaforma(...)
        }

        class DatiAzienda <<model>> {
            -idDatiAzienda: Long
            -nomeAzienda: String
            -partitaIva: String
            -indirizzoAzienda: String
            -certificazioniAzienda: List<Certificazione>
            --
            +getNomeAzienda(): String
        }
    }

    package "🛍️ Product Domain" <<model>> {
        class Prodotto <<model>> {
            -idProdotto: Long
            -nome: String
            -descrizione: String
            -prezzo: double
            -quantitaDisponibile: int
            -statoVerifica: StatoVerificaValori
            -venditore: Venditore
            -certificazioniProdotto: List<Certificazione>
            -tipoOrigine: TipoOrigineProdotto
            --
            +getId(): Long
            +getNome(): String
            +getPrezzo(): double
        }

        class Pacchetto <<model>> {
            -idPacchetto: Long
            -nome: String
            -descrizione: String
            -prezzoPacchetto: double
            -elementiInclusi: List<Acquistabile>
            -distributore: DistributoreDiTipicita
            --
            +getId(): Long
        }

        class Certificazione <<model>> {
            -idCertificazione: Long
            -nome: String
            -descrizione: String
            -enteEmettitore: String
            -dataScadenza: Date
            -statoVerifica: StatoVerificaValori
        }

        class MetodoDiColtivazione <<model>> {
            -id: Long
            -nome: String
            -descrizione: String
            -tipoPratica: String
        }
    }

    package "🛒 Order Domain" <<model>> {
        class Carrello <<model>> {
            -idCarrello: Long
            -acquirente: Acquirente
            -elementiCarrello: List<ElementoCarrello>
            -ultimaModifica: Date
            --
            +aggiungiElemento(elemento: ElementoCarrello): void
            +calcolaTotale(): double
        }

        class ElementoCarrello <<model>> {
            -idElemento: Long
            -acquistabile: Acquistabile
            -quantita: int
            -prezzoUnitario: double
            --
            +calcolaSottotale(): double
        }

        class Ordine <<model>> {
            -idOrdine: Long
            -dataOrdine: Date
            -importoTotale: double
            -acquirente: Acquirente
            -righeOrdine: List<RigaOrdine>
            -stato: IStatoOrdine
            --
            +aggiungiRigaOrdine(riga: RigaOrdine): void
            +getStatoOrdine(): StatoCorrente
        }

        class RigaOrdine <<model>> {
            -idRiga: Long
            -acquistabile: Acquistabile
            -quantita: int
            -prezzoUnitario: double
            -subtotale: double
            --
            +calcolaSubtotale(): void
        }
    }

    package "🔄 Transformation Domain" <<model>> {
        class Evento <<model>> {
            -id: Long
            -nome: String
            -descrizione: String
            -dataOraInizio: Date
            -luogo: String
            -organizzatore: AnimatoreDellaFiliera
            -aziendePartecipanti: List<Venditore>
        }

        class ProcessoTrasformazione <<model>> {
            -idProcesso: Long
            -nome: String
            -descrizione: String
            -metodoProduzione: String
            -fasi: List<FaseLavorazione>
            -trasformatore: Trasformatore
        }

        class FaseLavorazione <<model>> {
            -id: Long
            -nome: String
            -descrizione: String
            -ordineSequenza: int
            -fontiMateriePrime: List<FonteMateriaPrima>
        }

        class FonteInterna <<model>> {
            -prodotto: Prodotto
            -quantita: double
        }

        class FonteEsterna <<model>> {
            -nome: String
            -descrizione: String
            -quantita: double
            -fornitore: String
        }
    }
}

' === LAYER 4: COMMON (Cross-cutting Concerns) ===
package "⚙️ COMMON LAYER" <<common>> {
    note top : "Cross-cutting Concerns - Pattern, Interfacce, Contratti e Utility\n🔼 Utilizzato da: Tutti i layer superiori"

    package "🔗 Domain Interfaces" <<common>> {
        interface Acquistabile <<pattern>> {
            +getId(): Long
            +getNome(): String
            +getDescrizione(): String
            +getPrezzo(): double
            +getVenditore(): Venditore
        }

        interface ElementoVerificabile <<pattern>> {
            +getStatoVerifica(): StatoVerificaValori
            +setStatoVerifica(stato: StatoVerificaValori): void
            +getFeedbackVerifica(): String
            +setFeedbackVerifica(feedback: String): void
        }

        interface FonteMateriaPrima <<pattern>> {
            +getQuantita(): double
            +getTipo(): String
            +getDescrizione(): String
        }
    }

    package "🎭 Behavioral Patterns" <<common>> {
        interface IStatoOrdine <<pattern>> {
            +getStato(): StatoCorrente
            +prossimo(): IStatoOrdine
            +precedente(): IStatoOrdine
        }

        interface IMetodoPagamentoStrategy <<pattern>> {
            +paga(importo: double): boolean
        }

        interface IProdottoObservable <<pattern>> {
            +aggiungiObserver(observer: ICuratoreObserver): void
            +notificaObservers(prodotto: Prodotto): void
        }

        interface ICuratoreObserver <<pattern>> {
            +notificaNuovoProdotto(prodotto: Prodotto): void
        }

        interface IOrdineObservable <<pattern>> {
            +aggiungiObserver(observer: IVenditoreObserver): void
            +notificaObservers(ordine: Ordine): void
        }

        interface IVenditoreObserver <<pattern>> {
            +notificaNuovoOrdine(ordine: Ordine): void
        }

        interface UtenteFactory <<pattern>> {
            +creaAcquirente(...): Acquirente
            +creaProduttore(...): Produttore
            +creaDistributore(...): DistributoreDiTipicita
        }
    }

    package "📋 Domain Enumerations" <<common>> {
        enum TipoRuolo <<pattern>> {
            ACQUIRENTE, PRODUTTORE, DISTRIBUTORE
            TRASFORMATORE, CURATORE, ANIMATORE, GESTORE
        }

        enum StatoAccreditamento <<pattern>> {
            IN_ATTESA, ACCREDITATO, RIFIUTATO
        }

        enum StatoVerificaValori <<pattern>> {
            IN_ATTESA, VERIFICATO, RIFIUTATO
        }

        enum StatoCorrente <<pattern>> {
            NUOVO_IN_ATTESA_DI_PAGAMENTO
            PAGATO_PRONTO_PER_LAVORAZIONE
            IN_LAVORAZIONE, SPEDITO, CONSEGNATO, ANNULLATO
        }

        enum TipoOrigineProdotto <<pattern>> {
            PRIMARIO, TRASFORMATO
        }

        enum StatoEventoValori <<pattern>> {
            PROGRAMMATO, IN_CORSO, TERMINATO, ANNULLATO
        }
    }
}

' === LAYER 5: DTO (Data Transfer Objects) ===
package "📦 DTO LAYER" <<dto>> {
    note top : "Data Transfer Objects - Trasferimento dati tra layer e API\n🔼 Creato da: Service Layer (mapping)"

    class ProcessoTrasformazioneDTO <<dto>> {
        -idProcesso: Long
        -nome: String
        -descrizione: String
        -metodoProduzione: String
        -fasi: List<FaseLavorazioneDTO>
    }

    class FaseLavorazioneDTO <<dto>> {
        -id: Long
        -nome: String
        -descrizione: String
        -ordineSequenza: int
        -fontiMateriePrime: List<FonteMateriaPrima>
    }
}

' === LAYER 6: EXCEPTIONS ===
package "⚠️ EXCEPTION LAYER" <<common>> {
    note top : "Domain Exceptions - Gestione errori specifici del dominio"

    class OrdineException <<pattern>>
    class CarrelloVuotoException <<pattern>>
    class QuantitaNonDisponibileException <<pattern>>
    class QuantitaNonDisponibileAlCheckoutException <<pattern>>
    class PagamentoException <<pattern>>
}

' ========================================================================
' === 🔗 RELAZIONI ARCHITETTURALI E FLUSSI DI DIPENDENZA ===
' ========================================================================

' === 🔽 DIPENDENZE LAYER: SERVICE → REPOSITORY (Dependency Injection) ===
ProdottoService -[#FF5722,thickness=3]down-> IProdottoRepository : "🔽 dipende da\n(Dependency Injection)"
OrdineService -[#FF5722,thickness=3]down-> IOrdineRepository : "🔽 dipende da"
OrdineService -[#FF5722,thickness=3]down-> IRigaOrdineRepository : "🔽 dipende da"
CarrelloService -[#FF5722,thickness=3]down-> ICarrelloRepository : "🔽 dipende da"
VenditoreService -[#FF5722,thickness=3]down-> IVenditoreRepository : "🔽 dipende da"
VenditoreService -[#FF5722,thickness=3]down-> IDatiAziendaRepository : "🔽 dipende da"
CuratoreService -[#FF5722,thickness=3]down-> IVenditoreRepository : "🔽 dipende da"
CuratoreService -[#FF5722,thickness=3]down-> IProdottoRepository : "🔽 dipende da"
EventoService -[#FF5722,thickness=3]down-> IEventoRepository : "🔽 dipende da"
ProcessoTrasformazioneService -[#FF5722,thickness=3]down-> IProcessoTrasformazioneRepository : "🔽 dipende da"
CertificazioneService -[#FF5722,thickness=3]down-> ICertificazioneRepository : "🔽 dipende da"
PacchettoService -[#FF5722,thickness=3]down-> IPacchettoRepository : "🔽 dipende da"
GestoreService -[#FF5722,thickness=3]down-> IUtenteBaseRepository : "🔽 dipende da"

' === 🔄 COLLABORAZIONI SERVICE ↔ SERVICE (Business Logic Orchestration) ===
OrdineService -[#2196F3,thickness=2]right-> CarrelloService : "🔄 orchestra\n(business logic)"
VenditoreService -[#2196F3,thickness=2]right-> CertificazioneService : "🔄 collabora"
ProdottoService -[#2196F3,thickness=2]right-> CertificazioneService : "🔄 collabora"

' === 📡 NOTIFICHE OBSERVER PATTERN ===
CuratoreObserverService -[#9C27B0,thickness=2]up-> CuratoreService : "📡 notifica\n(Observer Pattern)"
VenditoreObserverService -[#9C27B0,thickness=2]up-> ProdottoService : "📡 notifica"
ProdottoService -[#9C27B0,thickness=2]up-> CuratoreObserverService : "📡 notifica nuovo prodotto"
OrdineService -[#9C27B0,thickness=2]up-> VenditoreObserverService : "📡 notifica nuovo ordine"

' === 🔽 DIPENDENZE LAYER: REPOSITORY → MODEL (Data Persistence) ===
IProdottoRepository -[#FF5722,thickness=3]down-> Prodotto : "🔽 persiste/recupera"
IOrdineRepository -[#FF5722,thickness=3]down-> Ordine : "🔽 persiste/recupera"
IRigaOrdineRepository -[#FF5722,thickness=3]down-> RigaOrdine : "🔽 persiste/recupera"
ICarrelloRepository -[#FF5722,thickness=3]down-> Carrello : "🔽 persiste/recupera"
IVenditoreRepository -[#FF5722,thickness=3]down-> Venditore : "🔽 persiste/recupera"
ICertificazioneRepository -[#FF5722,thickness=3]down-> Certificazione : "🔽 persiste/recupera"
IEventoRepository -[#FF5722,thickness=3]down-> Evento : "🔽 persiste/recupera"
IProcessoTrasformazioneRepository -[#FF5722,thickness=3]down-> ProcessoTrasformazione : "🔽 persiste/recupera"
IPacchettoRepository -[#FF5722,thickness=3]down-> Pacchetto : "🔽 persiste/recupera"
IDatiAziendaRepository -[#FF5722,thickness=3]down-> DatiAzienda : "🔽 persiste/recupera"

' === 🔀 TRASFORMAZIONI MODEL → DTO (Data Mapping) ===
ProcessoMapper -[#FF9800,thickness=2]down-> ProcessoTrasformazioneDTO : "🔀 crea DTO"
ProcessoMapper -[#FF9800,thickness=2]down-> ProcessoTrasformazione : "🔀 mappa da Model"

' === 🏗️ RELAZIONI DEL DOMINIO (Domain Model Relations) ===

' === Ereditarietà delle Entità Utente ===
Utente <|-[#4CAF50,thickness=2]- Acquirente : "🏗️ eredita"
Utente <|-[#4CAF50,thickness=2]- Venditore : "🏗️ eredita"
Utente <|-[#4CAF50,thickness=2]- Curatore : "🏗️ eredita"
Utente <|-[#4CAF50,thickness=2]- AnimatoreDellaFiliera : "🏗️ eredita"
Utente <|-[#4CAF50,thickness=2]- GestorePiattaforma : "🏗️ eredita"

' === Specializzazioni Venditore ===
Venditore <|-[#4CAF50,thickness=2]- Produttore : "🏗️ specializza"
Venditore <|-[#4CAF50,thickness=2]- DistributoreDiTipicita : "🏗️ specializza"
Venditore <|-[#4CAF50,thickness=2]- Trasformatore : "🏗️ specializza"

' === Implementazioni Interfacce ===
Acquistabile <|..[#673AB7,thickness=2]. Prodotto : "🔗 implementa"
Acquistabile <|..[#673AB7,thickness=2]. Pacchetto : "🔗 implementa"
Acquistabile <|..[#673AB7,thickness=2]. Evento : "🔗 implementa"
ElementoVerificabile <|..[#673AB7,thickness=2]. Prodotto : "🔗 implementa"
FonteMateriaPrima <|..[#673AB7,thickness=2]. FonteInterna : "🔗 implementa"
FonteMateriaPrima <|..[#673AB7,thickness=2]. FonteEsterna : "🔗 implementa"

' === Aggregazioni e Composizioni ===
Venditore *-[#795548,thickness=2]- DatiAzienda : "🏠 possiede"
Carrello *-[#795548,thickness=2]- ElementoCarrello : "📦 contiene"
Ordine *-[#795548,thickness=2]- RigaOrdine : "📦 contiene"
ProcessoTrasformazione *-[#795548,thickness=2]- FaseLavorazione : "🔧 compone"
DatiAzienda o-[#795548,thickness=2]- Certificazione : "📜 ha"

' === Implementazioni Pattern Comportamentali ===
IProdottoObservable <|..[#673AB7,thickness=2]. ProdottoService : "🔗 implementa Observer"
IOrdineObservable <|..[#673AB7,thickness=2]. OrdineService : "🔗 implementa Observer"
ICuratoreObserver <|..[#673AB7,thickness=2]. CuratoreObserverService : "🔗 implementa Observer"
IVenditoreObserver <|..[#673AB7,thickness=2]. VenditoreObserverService : "🔗 implementa Observer"

' === NOTA ARCHITETTURALE ===
note bottom : "🎯 FLUSSO ARCHITETTURALE:\n🔽 Service Layer → Repository Layer → Model Layer\n🔄 Service ↔ Service (orchestrazione business logic)\n📡 Observer Pattern (notifiche asincrone)\n🔗 Common Layer utilizzato trasversalmente da tutti i layer"

@enduml
```

## 🎯 Miglioramenti Implementati

### 1. **Connessioni Visive Rafforzate**

- ✅ **Frecce colorate e spesse** per distinguere i tipi di relazione
- ✅ **Etichette descrittive** con emoji per identificare rapidamente il tipo di dipendenza
- ✅ **Colori specifici**:
  - 🔴 Rosso per dipendenze architetturali (Service → Repository → Model)
  - 🔵 Blu per collaborazioni tra Service
  - 🟣 Viola per notifiche Observer Pattern
  - 🟢 Verde per ereditarietà
  - 🟤 Marrone per aggregazioni/composizioni

### 2. **Struttura Layer Migliorata**

- ✅ **Raggruppamento logico** dei componenti per responsabilità
- ✅ **Note esplicative** per ogni layer che descrivono ruolo e dipendenze
- ✅ **Emoji identificative** per ogni package per riconoscimento immediato
- ✅ **Separatori visivi** tra attributi e metodi nelle classi

### 3. **Flusso Architetturale Chiaro**

- ✅ **Direzione delle dipendenze** chiaramente indicata (dall'alto verso il basso)
- ✅ **Orchestrazione business logic** evidenziata tra Service
- ✅ **Pattern comportamentali** (Observer, Strategy, Factory) ben collegati
- ✅ **Mapping DTO** chiaramente separato

### 4. **Eliminazione Duplicazioni**

- ✅ **Definizioni uniche** per ogni classe/interfaccia
- ✅ **Struttura consolidata** senza ripetizioni
- ✅ **Relazioni centralizzate** in sezioni dedicate

### 5. **Leggibilità Migliorata**

- ✅ **Colori distintivi** per ogni layer
- ✅ **Tipografia migliorata** con separatori e formattazione
- ✅ **Note architetturali** che spiegano il flusso complessivo

## 📋 Legenda delle Relazioni

### 🔽 **Dipendenze Architetturali** (Rosso)

- **Service → Repository**: I service dipendono dai repository per l'accesso ai dati
- **Repository → Model**: I repository gestiscono la persistenza delle entità del dominio

### 🔄 **Collaborazioni Business** (Blu)

- **Service ↔ Service**: Orchestrazione della logica di business tra diversi service
- Esempio: `OrdineService` orchestra `CarrelloService` per creare ordini

### 📡 **Notifiche Observer** (Viola)

- **Pattern Observer**: Notifiche asincrone tra componenti
- Esempio: Nuovo prodotto → Notifica al curatore per verifica

### 🏗️ **Ereditarietà** (Verde)

- **Gerarchia classi**: Relazioni di ereditarietà nel dominio
- Esempio: `Produttore` eredita da `Venditore`

### 🔗 **Implementazioni** (Viola chiaro)

- **Interfacce**: Implementazione di contratti e pattern
- Esempio: `Prodotto` implementa `Acquistabile`

### 🏠 **Aggregazioni/Composizioni** (Marrone)

- **Relazioni strutturali**: Contenimento e possesso tra entità
- Esempio: `Venditore` possiede `DatiAzienda`

## 🎯 Benefici dell'Architettura Migliorata

1. **Chiarezza delle Dipendenze**: Ogni freccia ha un significato specifico e un colore distintivo
2. **Flusso Unidirezionale**: Le dipendenze vanno sempre dall'alto verso il basso tra i layer
3. **Separazione delle Responsabilità**: Ogni layer ha un ruolo ben definito
4. **Facilità di Manutenzione**: Le modifiche seguono il flusso delle dipendenze
5. **Testabilità**: L'inversione delle dipendenze facilita il testing
6. **Scalabilità**: Nuovi componenti possono essere aggiunti seguendo i pattern esistenti

---

## 🚀 Conclusioni

Il diagramma architetturale migliorato della piattaforma agricola locale rappresenta ora un **sistema coeso e ben strutturato** con:

### ✅ **Architettura Chiara e Comprensibile**

- **Flusso unidirezionale** delle dipendenze (Service → Repository → Model)
- **Separazione netta** delle responsabilità tra i layer
- **Connessioni visive** che rendono immediatamente comprensibili le relazioni

### ✅ **Facilità di Manutenzione e Sviluppo**

- **Pattern consolidati** (Observer, Strategy, Factory) ben integrati
- **Dependency Injection** per facilitare testing e mocking
- **Struttura modulare** che permette modifiche localizzate

### ✅ **Scalabilità e Estensibilità**

- **Nuovi Service** possono essere aggiunti facilmente
- **Nuove entità** seguono i pattern esistenti
- **Observer Pattern** permette di aggiungere nuove notifiche senza modificare il codice esistente

### 🎯 **Prossimi Passi Suggeriti**

1. **Implementazione graduale** seguendo l'ordine dei layer (Model → Repository → Service)
2. **Test unitari** per ogni Service utilizzando mock dei Repository
3. **Integrazione continua** per verificare le dipendenze tra layer
4. **Documentazione API** per i Service pubblici

Il diagramma è ora **pronto per l'implementazione** e fornisce una guida chiara per lo sviluppo del sistema.
        class ProdottoService implements IProdottoObservable {
            -prodottoRepository: IProdottoRepository
            -certificazioneService: ICertificazioneService
            -venditoreRepository: IVenditoreRepository
            -observers: List<ICuratoreObserver>
            +creaProdotto(...): Prodotto
            +aggiornaProdotto(prodotto: Prodotto): Prodotto
            +mostraTuttiIProdotti(): List<Prodotto>
            +aggiungiObserver(observer: ICuratoreObserver): void
            +notificaObservers(prodotto: Prodotto): void
        }

        class PacchettoService {
            -pacchettoRepository: IPacchettoRepository
            -venditoreRepository: IVenditoreRepository
            +creaPacchetto(...): Pacchetto
            +aggiornaPacchetto(pacchetto: Pacchetto): Pacchetto
            +aggiungiElementoAPacchetto(...): void
            +mostraTuttiIPacchetti(): List<Pacchetto>
        }
        
        class OrdineService implements IOrdineObservable {
            -ordineRepository: OrdineRepository
            -rigaOrdineRepository: RigaOrdineRepository
            -carrelloService: CarrelloService
            -observers: List<IVenditoreObserver>
            +creaOrdineDaCarrello(acquirente: Acquirente): Ordine
            +confermaOrdine(ordine: Ordine): void
            +cambiaStatoOrdine(ordine: Ordine): void
            +pagaOrdine(ordine: Ordine, metodoPagamento: IMetodoPagamentoStrategy): boolean
            +aggiungiObserver(observer: IVenditoreObserver): void
            +notificaObservers(ordine: Ordine): void
        }
        
        class CarrelloService {
            -carrelloRepository: CarrelloRepository
            +creaNuovoCarrello(acquirente: Acquirente): Carrello
            +aggiungiAlCarrello(acquirente: Acquirente, acquistabile: Acquistabile, quantita: int): void
            +ottieniCarrello(acquirente: Acquirente): Optional<Carrello>
            +svuotaCarrello(acquirente: Acquirente): void
        }
        
        class RigaOrdineService {
            -rigaOrdineRepository: RigaOrdineRepository
            +creaRigaOrdine(...): RigaOrdine
            +getRighePerOrdine(ordine: Ordine): List<RigaOrdine>
            +calcolaSubtotale(rigaOrdine: RigaOrdine): double
        }
    }

    package "User Management Services" <<service>> {
        class VenditoreService {
            -venditoreRepository: IVenditoreRepository
            -certificazioneService: ICertificazioneService
            -datiAziendaRepository: IDatiAziendaRepository
            +aggiornaDatiAzienda(venditore: Venditore, datiAggiornati: DatiAzienda): void
            +aggiungiDatiAzienda(...): DatiAzienda
            +aggiungiCertificazioneAzienda(...): void
        }

        class ProduttoreService extends VenditoreService {
            +ProduttoreService(...)
        }
        
        class CuratoreService {
            -venditoreRepository: IVenditoreRepository
            -prodottoRepository: IProdottoRepository
            -datiAziendaRepository: IDatiAziendaRepository
            -codaRevisioneProdotti: Queue<Prodotto>
            +verificaProdotto(prodotto: Prodotto, approvato: boolean, feedback: String): void
            +verificaDatiAzienda(datiAzienda: DatiAzienda, approvato: boolean): void
            +ottieniProdottiInAttesaDiRevisione(): List<Prodotto>
        }
        
        class GestoreService {
            -utenteBaseRepository: UtenteBaseRepository
            -venditoreRepository: VenditoreRepository
            -curatoreRepository: CuratoreRepository
            -animatoreRepository: AnimatoreRepository
            -datiAziendaRepository: DatiAziendaRepository
            +getVenditoriInAttesaDiAccreditamento(): List<Venditore>
            +aggiornaStatoAccreditamentoVenditore(...): boolean
            +attivaDisattivaAcquirente(...): boolean
        }
    }

    package "Specialized Services" <<service>> {
        class EventoService {
            -eventoRepository: IEventoRepository
            +creaEvento(...): void
            +aggiornaEvento(...): void
            +aggiungiAziendaPartecipante(...): void
            +ottieniEventiPerOrganizzatore(...): List<Evento>
        }

        class ProcessoTrasformazioneService {
            -processoRepository: IProcessoTrasformazioneRepository
            +creaProcesso(...): ProcessoTrasformazione
            +aggiungiFaseAlProcesso(...): ProcessoTrasformazione
            +ottieniProcessiPerTrasformatore(...): List<ProcessoTrasformazione>
        }
        
        class CertificazioneService {
            -certificazioneRepository: ICertificazioneRepository
            +creaCertificazione(...): Certificazione
            +verificaCertificazione(certificazione: Certificazione): boolean
            +getAllCertificazioni(): List<Certificazione>
        }
    }

    package "Observer Services" <<service>> {
        class CuratoreObserverService implements ICuratoreObserver {
            -curatoreService: CuratoreService
            +notificaNuovoProdotto(prodotto: Prodotto): void
        }

        class VenditoreObserverService implements IVenditoreObserver {
            -prodottoService: ProdottoService
            -pacchettoService: PacchettoService
            +notificaNuovoOrdine(ordine: Ordine): void
        }
    }

    package "Mapper Services" <<service>> {
        class ProcessoMapper {
            +toDto(processo: ProcessoTrasformazione): ProcessoTrasformazioneDTO
        }
    }
}

package "LAYER: DTO (Data Transfer)" <<common>> {
    class ProcessoTrasformazioneDTO {
        -idProcesso: Long
        -nome: String
        -descrizione: String
        -metodoProduzione: String
        -fasi: List<FaseLavorazioneDTO>
        +ProcessoTrasformazioneDTO(...)
    }

    class FaseLavorazioneDTO {
        -id: Long
        -nome: String
        -descrizione: String
        -ordineSequenza: int
        -fontiMateriePrime: List<FonteMateriaPrima>
        +FaseLavorazioneDTO(...)
    }
}

package "LAYER: EXCEPTIONS" <<common>> {
    class OrdineException extends Exception
    class CarrelloVuotoException extends RuntimeException
    class QuantitaNonDisponibileException extends RuntimeException
    class QuantitaNonDisponibileAlCheckoutException extends RuntimeException
    class PagamentoException extends Exception
}

' === INTERFACCE STRATEGY PATTERN ===
interface IMetodoPagamentoStrategy {
    +paga(importo: double): boolean
}

' === INTERFACCE FACTORY PATTERN ===
interface UtenteFactory {
    +creaAcquirente(nome: String, cognome: String, email: String, password: String, telefono: String): Acquirente
    +creaProduttore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): Produttore
    +creaDistributore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): DistributoreDiTipicita
    +creaTrasformatore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): Trasformatore
    +creaCuratore(nome: String, cognome: String, email: String, password: String, telefono: String): Curatore
    +creaAnimatore(nome: String, cognome: String, email: String, password: String, telefono: String): AnimatoreDellaFiliera
    +creaGestore(nome: String, cognome: String, email: String, password: String, telefono: String): GestorePiattaforma
}

' === INTERFACCE REPOSITORY ===
interface IProdottoRepository {
    +save(prodotto: Prodotto): void
    +findById(id: Long): Prodotto
    +mostraTuttiIProdotti(): List<Prodotto>
    +deleteById(id: Long): void
    +findByVenditore(venditore: Venditore): List<Prodotto>
    +findByNome(nome: String): List<Prodotto>
}

interface IPacchettoRepository {
    +salva(pacchetto: Pacchetto): void
    +findById(id: Long): Pacchetto
    +mostraTuttiIPacchetti(): List<Pacchetto>
    +deleteById(id: Long): void
    +findByVenditore(venditore: Venditore): List<Pacchetto>
    +findByNome(nome: String): List<Pacchetto>
}

interface IOrdineRepository {
    +save(ordine: Ordine): void
    +findById(idOrdine: Long): Optional<Ordine>
    +findAll(): List<Ordine>
    +findByAcquirente(acquirente: Acquirente): List<Ordine>
    +findByStato(stato: StatoCorrente): List<Ordine>
    +findByVenditore(venditore: Venditore): List<Ordine>
    +deleteById(idOrdine: Long): void
    +update(ordine: Ordine): void
}

interface IRigaOrdineRepository {
    +save(rigaOrdine: RigaOrdine): void
    +findById(idRiga: Long): Optional<RigaOrdine>
    +findAll(): List<RigaOrdine>
    +findByOrdine(ordine: Ordine): List<RigaOrdine>
    +findByAcquistabile(acquistabile: Acquistabile): List<RigaOrdine>
    +deleteById(idRiga: Long): void
    +deleteByOrdine(ordine: Ordine): void
    +update(rigaOrdine: RigaOrdine): void
}

interface ICarrelloRepository {
    +save(carrello: Carrello): void
    +findById(idCarrello: Long): Optional<Carrello>
    +findByAcquirente(acquirente: Acquirente): Optional<Carrello>
    +findAll(): List<Carrello>
    +deleteById(idCarrello: Long): void
    +deleteByAcquirente(acquirente: Acquirente): void
}

interface IVenditoreRepository {
    +save(venditore: Venditore): void
    +findById(id: Long): Optional<Venditore>
    +findAll(): List<Venditore>
    +findByStatoAccreditamento(stato: StatoAccreditamento): List<Venditore>
}

interface ICertificazioneRepository {
    +save(certificazione: Certificazione): void
    +findById(id: Long): Optional<Certificazione>
    +findAll(): List<Certificazione>
    +deleteById(id: Long): void
}

interface IProcessoTrasformazioneRepository {
    +save(processo: ProcessoTrasformazione): void
    +findById(id: Long): Optional<ProcessoTrasformazione>
    +findAll(): List<ProcessoTrasformazione>
    +findByTrasformatore(trasformatore: Trasformatore): List<ProcessoTrasformazione>
    +deleteById(id: Long): void
}

interface IEventoRepository {
    +save(evento: Evento): void
    +findById(id: Long): Evento
    +mostraTuttiEventi(): List<Evento>
    +deleteById(id: Long): void
    +findByAnimatoreId(organizzatore: AnimatoreDellaFiliera): List<Evento>
    +findByNome(nome: String): List<Evento>
    +findByAziendaPartecipante(venditore: Venditore): List<Evento>
}

interface IAnimatoreRepository {
    +save(animatore: AnimatoreDellaFiliera): void
    +findById(id: Long): Optional<AnimatoreDellaFiliera>
    +findAll(): List<AnimatoreDellaFiliera>
    +findByStatoAccreditamento(stato: StatoAccreditamento): List<AnimatoreDellaFiliera>
}

interface ICuratoreRepository {
    +save(curatore: Curatore): void
    +findById(id: Long): Optional<Curatore>
    +findAll(): List<Curatore>
    +findByStatoAccreditamento(stato: StatoAccreditamento): List<Curatore>
}

interface IDatiAziendaRepository {
    +findById(id: Long): Optional<DatiAzienda>
    +findByPartitaIva(partitaIva: String): Optional<DatiAzienda>
    +save(datiAzienda: DatiAzienda): void
    +findAll(): List<DatiAzienda>
    +deleteById(partitaIva: String): void
}

interface IUtenteBaseRepository {
    +save(utente: Utente): void
    +findById(id: Long): Optional<Utente>
    +findAll(): List<Utente>
    +findByEmail(email: String): Optional<Utente>
    +deleteById(id: Long): void
}

interface IMetodoDiColtivazioneRepository {
    +save(metodo: MetodoDiColtivazione): void
    +findById(id: Long): Optional<MetodoDiColtivazione>
    +findAll(): List<MetodoDiColtivazione>
    +deleteById(id: Long): void
}

' === CLASSI MODELLO ===
abstract class Utente {
    -idUtente: Long
    -nome: String
    -cognome: String
    -email: String
    -passwordHash: String
    -numeroTelefono: String
    -tipoRuolo: TipoRuolo
    -isAttivo: boolean
    +Utente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo)
    +getNome(): String
    +setNome(nome: String): void
    +getCognome(): String
    +setCognome(cognome: String): void
    +getEmail(): String
    +setEmail(email: String): void
    +getPasswordHash(): String
    +setPasswordHash(passwordHash: String): void
    +getNumeroTelefono(): String
    +setNumeroTelefono(numeroTelefono: String): void
    +getTipoRuolo(): TipoRuolo
    +setTipoRuolo(tipoRuolo: TipoRuolo): void
    +isAttivo(): boolean
    +setAttivo(attivo: boolean): void
    +getId(): Long
    +setId(id: Long): void
}

class Acquirente extends Utente {
    +Acquirente(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, tipoRuolo: TipoRuolo, attivo: boolean)
}

abstract class Venditore extends Utente {
    -datiAzienda: DatiAzienda
    -prodottiOfferti: List<Prodotto>
    -statoAccreditamento: StatoAccreditamento
    +Venditore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda, tipoRuolo: TipoRuolo)
    +stampaDatiAzienda(): void
    +aggiungiProdotto(prodotto: Prodotto): void
    +rimuoviProdotto(prodotto: Prodotto): void
    +getDatiAzienda(): DatiAzienda
    +setDatiAzienda(datiAzienda: DatiAzienda): void
    +getProdottiOfferti(): List<Prodotto>
    +setProdottiOfferti(prodottiOfferti: List<Prodotto>): void
    +getStatoAccreditamento(): StatoAccreditamento
    +setStatoAccreditamento(statoAccreditamento: StatoAccreditamento): void
}

class Produttore extends Venditore {
    +Produttore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda)
}

class DistributoreDiTipicita extends Venditore {
    +DistributoreDiTipicita(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda)
}

class Trasformatore extends Venditore {
    -metodiDiTrasformazione: List<String>
    +Trasformatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, datiAzienda: DatiAzienda)
    +getMetodiDiTrasformazione(): List<String>
    +setMetodiDiTrasformazione(metodiDiTrasformazione: List<String>): void
    +aggiungiMetodoDiTrasformazione(metodo: String): void
    +rimuoviMetodoDiTrasformazione(metodo: String): void
}

class Curatore extends Utente {
    -specializzazione: String
    -annoAbilitazione: int
    -statoAccreditamento: StatoAccreditamento
    +Curatore(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, specializzazione: String, annoAbilitazione: int)
    +getSpecializzazione(): String
    +setSpecializzazione(specializzazione: String): void
    +getAnnoAbilitazione(): int
    +setAnnoAbilitazione(annoAbilitazione: int): void
    +getStatoAccreditamento(): StatoAccreditamento
    +setStatoAccreditamento(statoAccreditamento: StatoAccreditamento): void
}

class AnimatoreDellaFiliera extends Utente {
    -areaCompetenza: String
    -esperienza: int
    -statoAccreditamento: StatoAccreditamento
    +AnimatoreDellaFiliera(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String, areaCompetenza: String, esperienza: int)
    +getAreaCompetenza(): String
    +setAreaCompetenza(areaCompetenza: String): void
    +getEsperienza(): int
    +setEsperienza(esperienza: int): void
    +getStatoAccreditamento(): StatoAccreditamento
    +setStatoAccreditamento(statoAccreditamento: StatoAccreditamento): void
}

class GestorePiattaforma extends Utente {
    +GestorePiattaforma(nome: String, cognome: String, email: String, passwordHash: String, numeroTelefono: String)
}

class DatiAzienda {
    -idDatiAzienda: Long
    -nomeAzienda: String
    -partitaIva: String
    -indirizzoAzienda: String
    -descrizioneAzienda: String
    -logoAzienda: String
    -sitoWebAzienda: String
    -certificazioniAzienda: List<Certificazione>
    +DatiAzienda(idDatiAzienda: Long, nomeAzienda: String, partitaIva: String, indirizzoAzienda: String, descrizioneAzienda: String, logoAzienda: String, sitoWebAzienda: String)
    +aggiungiCertificazione(certificazione: Certificazione): void
    +rimuoviCertificazione(certificazione: Certificazione): void
    +stampaDatiAzienda(): void
    +getNomeAzienda(): String
    +setNomeAzienda(nomeAzienda: String): void
    +getPartitaIva(): String
    +setPartitaIva(partitaIva: String): void
    +getIndirizzoAzienda(): String
    +setIndirizzoAzienda(indirizzoAzienda: String): void
    +getDescrizioneAzienda(): String
    +setDescrizioneAzienda(descrizioneAzienda: String): void
    +getLogoAzienda(): String
    +setLogoAzienda(logoAzienda: String): void
    +getSitoWebAzienda(): String
    +setSitoWebAzienda(sitoWebAzienda: String): void
    +getCertificazioniAzienda(): List<Certificazione>
    +setCertificazioniAzienda(certificazioniAzienda: List<Certificazione>): void
    +getId(): Long
    +setId(id: Long): void
}

class Prodotto implements Acquistabile, ElementoVerificabile {
    -idProdotto: Long
    -nome: String
    -descrizione: String
    -prezzo: double
    -quantitaDisponibile: int
    -statoVerifica: StatoVerificaValori
    -feedbackVerifica: String
    -venditore: Venditore
    -certificazioniProdotto: List<Certificazione>
    -tipoOrigine: TipoOrigineProdotto
    -idProcessoTrasformazioneOriginario: Long
    -idMetodoDiColtivazione: Long
    +Prodotto(nome: String, descrizione: String, prezzo: double, quantitaDisponibile: int, venditore: Venditore)
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getPrezzo(): double
    +setPrezzo(prezzo: double): void
    +getQuantitaDisponibile(): int
    +setQuantitaDisponibile(quantitaDisponibile: int): void
    +getVenditore(): Venditore
    +setVenditore(venditore: Venditore): void
    +getCertificazioniProdotto(): List<Certificazione>
    +setCertificazioniProdotto(certificazioniProdotto: List<Certificazione>): void
    +getTipoOrigine(): TipoOrigineProdotto
    +setTipoOrigine(tipoOrigine: TipoOrigineProdotto): void
    +getStatoVerifica(): StatoVerificaValori
    +setStatoVerifica(statoVerifica: StatoVerificaValori): void
    +getFeedbackVerifica(): String
    +setFeedbackVerifica(feedbackVerifica: String): void
    +getIdProcessoTrasformazioneOriginario(): Long
    +setIdProcessoTrasformazioneOriginario(idProcessoTrasformazioneOriginario: Long): void
    +getIdMetodoDiColtivazione(): Long
    +setIdMetodoDiColtivazione(idMetodoDiColtivazione: Long): void
}

class Pacchetto implements Acquistabile {
    -idPacchetto: Long
    -nome: String
    -descrizione: String
    -quantitaDisponibile: int
    -prezzoPacchetto: double
    -elementiInclusi: List<Acquistabile>
    -distributore: DistributoreDiTipicita
    +Pacchetto(distributore: DistributoreDiTipicita, nome: String, descrizione: String, quantita: int, prezzoPacchetto: double)
    +getId(): Long
    +SetId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getPrezzo(): double
    +setPrezzo(prezzo: double): void
    +getQuantitaDisponibile(): int
    +setQuantitaDisponibile(quantitaDisponibile: int): void
    +getVenditore(): Venditore
    +getDistributore(): DistributoreDiTipicita
    +setDistributore(distributore: DistributoreDiTipicita): void
    +getElementiInclusi(): List<Acquistabile>
    +setElementiInclusi(elementiInclusi: List<Acquistabile>): void
    +aggiungiElemento(elemento: Acquistabile): void
    +rimuoviElemento(elemento: Acquistabile): void
}

class Certificazione {
    -idCertificazione: Long
    -nome: String
    -descrizione: String
    -enteEmettitore: String
    -dataScadenza: Date
    -statoVerifica: StatoVerificaValori
    +Certificazione(nome: String, descrizione: String, enteEmettitore: String, dataScadenza: Date)
    +stampaCertificazione(): void
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getEnteEmettitore(): String
    +setEnteEmettitore(enteEmettitore: String): void
    +getDataScadenza(): Date
    +setDataScadenza(dataScadenza: Date): void
    +getStatoVerifica(): StatoVerificaValori
    +setStatoVerifica(statoVerifica: StatoVerificaValori): void
}

class Carrello {
    -idCarrello: Long
    -acquirente: Acquirente
    -elementiCarrello: List<ElementoCarrello>
    -ultimaModifica: Date
    +Carrello(acquirente: Acquirente)
    +getIdCarrello(): Long
    +setIdCarrello(idCarrello: Long): void
    +getAcquirente(): Acquirente
    +setAcquirente(acquirente: Acquirente): void
    +getElementiCarrello(): List<ElementoCarrello>
    +setElementiCarrello(elementiCarrello: List<ElementoCarrello>): void
    +getUltimaModifica(): Date
    +setUltimaModifica(ultimaModifica: Date): void
    +aggiungiElemento(elemento: ElementoCarrello): void
    +rimuoviElemento(elemento: ElementoCarrello): void
    +calcolaTotale(): double
    +isEmpty(): boolean
    +svuota(): void
}

class ElementoCarrello {
    -idElemento: Long
    -acquistabile: Acquistabile
    -quantita: int
    -prezzoUnitario: double
    +ElementoCarrello(acquistabile: Acquistabile, quantita: int)
    +getIdElemento(): Long
    +setIdElemento(idElemento: Long): void
    +getAcquistabile(): Acquistabile
    +setAcquistabile(acquistabile: Acquistabile): void
    +getQuantita(): int
    +setQuantita(quantita: int): void
    +getPrezzoUnitario(): double
    +setPrezzoUnitario(prezzoUnitario: double): void
    +calcolaSottotale(): double
}

class Ordine {
    -idOrdine: Long
    -dataOrdine: Date
    -importoTotale: double
    -acquirente: Acquirente
    -righeOrdine: List<RigaOrdine>
    -stato: IStatoOrdine
    +Ordine(dataOrdine: Date, acquirente: Acquirente)
    +getIdOrdine(): Long
    +setIdOrdine(idOrdine: Long): void
    +getDataOrdine(): Date
    +setDataOrdine(dataOrdine: Date): void
    +getImportoTotale(): double
    +setImportoTotale(importoTotale: double): void
    +getAcquirente(): Acquirente
    +setAcquirente(acquirente: Acquirente): void
    +getRigheOrdine(): List<RigaOrdine>
    +setRigheOrdine(righeOrdine: List<RigaOrdine>): void
    +getStato(): IStatoOrdine
    +setStato(stato: IStatoOrdine): void
    +getStatoOrdine(): StatoCorrente
    +aggiungiRigaOrdine(riga: RigaOrdine): void
    +rimuoviRigaOrdine(riga: RigaOrdine): void
    +cambiaStato(): void
    +calcolaImportoTotale(): void
}

class RigaOrdine {
    -idRiga: Long
    -acquistabile: Acquistabile
    -quantita: int
    -prezzoUnitario: double
    -subtotale: double
    +RigaOrdine(acquistabile: Acquistabile, quantita: int, prezzoUnitario: double)
    +getIdRiga(): Long
    +setIdRiga(idRiga: Long): void
    +getAcquistabile(): Acquistabile
    +setAcquistabile(acquistabile: Acquistabile): void
    +getQuantita(): int
    +setQuantita(quantita: int): void
    +getPrezzoUnitario(): double
    +setPrezzoUnitario(prezzoUnitario: double): void
    +getSubtotale(): double
    +setSubtotale(subtotale: double): void
    +calcolaSubtotale(): void
}

class Evento implements Acquistabile {
    -id: Long
    -nome: String
    -descrizione: String
    -dataOraInizio: Date
    -dataOraFine: Date
    -luogo: String
    -capienzaMassima: int
    -postiPrenotati: int
    -stato: StatoEventoValori
    -organizzatore: AnimatoreDellaFiliera
    -aziendePartecipanti: List<Venditore>
    +Evento(nome: String, descrizione: String, dataOraInizio: Date, dataOraFine: Date, luogo: String, capienzaMassima: int, organizzatore: AnimatoreDellaFiliera)
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getPrezzo(): double
    +getVenditore(): Venditore
    +getDataOraInizio(): Date
    +setDataOraInizio(dataOraInizio: Date): void
    +getDataOraFine(): Date
    +setDataOraFine(dataOraFine: Date): void
    +getLuogo(): String
    +setLuogo(luogo: String): void
    +getCapienzaMassima(): int
    +setCapienzaMassima(capienzaMassima: int): void
    +getPostiPrenotati(): int
    +setPostiPrenotati(postiPrenotati: int): void
    +getStato(): StatoEventoValori
    +setStato(stato: StatoEventoValori): void
    +getOrganizzatore(): AnimatoreDellaFiliera
    +setOrganizzatore(organizzatore: AnimatoreDellaFiliera): void
    +getAziendePartecipanti(): List<Venditore>
    +setAziendePartecipanti(aziendePartecipanti: List<Venditore>): void
    +aggiungiAziendaPartecipante(venditore: Venditore): void
    +rimuoviAziendaPartecipante(venditore: Venditore): void
    +prenotaPosti(quantita: int): void
    +liberaPosti(quantita: int): void
}

class ProcessoTrasformazione {
    -idProcesso: Long
    -nome: String
    -descrizione: String
    -metodoProduzione: String
    -note: String
    -fasi: List<FaseLavorazione>
    -trasformatore: Trasformatore
    +ProcessoTrasformazione(nome: String, descrizione: String, trasformatore: Trasformatore, metodoProduzione: String)
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getMetodoProduzione(): String
    +setMetodoProduzione(metodoProduzione: String): void
    +getNote(): String
    +setNote(note: String): void
    +getFasi(): List<FaseLavorazione>
    +setFasi(fasi: List<FaseLavorazione>): void
    +getTrasformatore(): Trasformatore
    +setTrasformatore(trasformatore: Trasformatore): void
    +aggiungiFase(fase: FaseLavorazione): void
    +rimuoviFase(fase: FaseLavorazione): void
}

class FaseLavorazione {
    -id: Long
    -nome: String
    -descrizione: String
    -ordineSequenza: int
    -temperaturaProcesso: double
    -tempoLavorazione: int
    -fontiMateriePrime: List<FonteMateriaPrima>
    +FaseLavorazione(nome: String, descrizione: String, ordineSequenza: int, temperaturaProcesso: double, tempoLavorazione: int)
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getOrdineSequenza(): int
    +setOrdineSequenza(ordineSequenza: int): void
    +getTemperaturaProcesso(): double
    +setTemperaturaProcesso(temperaturaProcesso: double): void
    +getTempoLavorazione(): int
    +setTempoLavorazione(tempoLavorazione: int): void
    +getFontiMateriePrime(): List<FonteMateriaPrima>
    +setFontiMateriePrime(fontiMateriePrime: List<FonteMateriaPrima>): void
    +aggiungiFonteMateriaPrima(fonte: FonteMateriaPrima): void
    +rimuoviFonteMateriaPrima(fonte: FonteMateriaPrima): void
}

class FonteInterna implements FonteMateriaPrima {
    -prodotto: Prodotto
    -quantita: double
    +FonteInterna(prodotto: Prodotto, quantita: double)
    +getProdotto(): Prodotto
    +setProdotto(prodotto: Prodotto): void
    +getQuantita(): double
    +setQuantita(quantita: double): void
    +getTipo(): String
    +getDescrizione(): String
}

class FonteEsterna implements FonteMateriaPrima {
    -nome: String
    -descrizione: String
    -quantita: double
    -fornitore: String
    +FonteEsterna(nome: String, descrizione: String, quantita: double, fornitore: String)
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getQuantita(): double
    +setQuantita(quantita: double): void
    +getFornitore(): String
    +setFornitore(fornitore: String): void
    +getTipo(): String
}

class MetodoDiColtivazione {
    -id: Long
    -nome: String
    -descrizione: String
    -tipoPratica: String
    -certificazioniRichieste: List<String>
    +MetodoDiColtivazione(nome: String, descrizione: String, tipoPratica: String)
    +getId(): Long
    +setId(id: Long): void
    +getNome(): String
    +setNome(nome: String): void
    +getDescrizione(): String
    +setDescrizione(descrizione: String): void
    +getTipoPratica(): String
    +setTipoPratica(tipoPratica: String): void
    +getCertificazioniRichieste(): List<String>
    +setCertificazioniRichieste(certificazioniRichieste: List<String>): void
}

' === CLASSI STATE PATTERN ===
class StatoOrdineNuovoInAttesaDiPagamento implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

class StatoOrdinePagatoProntoPerLavorazione implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

class StatoOrdineInLavorazione implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

class StatoOrdineSpedito implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

class StatoOrdineConsegnato implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

class StatoOrdineAnnullato implements IStatoOrdine {
    +getStato(): StatoCorrente
    +prossimo(): IStatoOrdine
    +precedente(): IStatoOrdine
}

' === CLASSI STRATEGY PATTERN ===
class PagamentoCartaCreditoStrategy implements IMetodoPagamentoStrategy {
    -numeroCartaCredito: String
    -dataScadenza: String
    -cvv: String
    +PagamentoCartaCreditoStrategy(numeroCartaCredito: String, dataScadenza: String, cvv: String)
    +paga(importo: double): boolean
}

class PagamentoPayPalStrategy implements IMetodoPagamentoStrategy {
    -emailPayPal: String
    -passwordPayPal: String
    +PagamentoPayPalStrategy(emailPayPal: String, passwordPayPal: String)
    +paga(importo: double): boolean
}

class PagamentoSimulatoStrategy implements IMetodoPagamentoStrategy {
    +paga(importo: double): boolean
}

' === CLASSI FACTORY ===
class SimpleUtenteFactory implements UtenteFactory {
    +creaAcquirente(nome: String, cognome: String, email: String, password: String, telefono: String): Acquirente
    +creaProduttore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): Produttore
    +creaDistributore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): DistributoreDiTipicita
    +creaTrasformatore(nome: String, cognome: String, email: String, password: String, telefono: String, datiAzienda: DatiAzienda): Trasformatore
    +creaCuratore(nome: String, cognome: String, email: String, password: String, telefono: String): Curatore
    +creaAnimatore(nome: String, cognome: String, email: String, password: String, telefono: String): AnimatoreDellaFiliera
    +creaGestore(nome: String, cognome: String, email: String, password: String, telefono: String): GestorePiattaforma
}

' === CLASSI SERVICE ===
class CarrelloService {
    -carrelloRepository: CarrelloRepository
    +CarrelloService()
    +CarrelloService(carrelloRepository: CarrelloRepository)
    +creaNuovoCarrello(acquirente: Acquirente): Carrello
    +ottieniCarrello(acquirente: Acquirente): Optional<Carrello>
    +aggiungiAlCarrello(acquirente: Acquirente, acquistabile: Acquistabile, quantita: int): void
    +rimuoviDalCarrello(acquirente: Acquirente, acquistabile: Acquistabile): void
    +aggiornaQuantita(acquirente: Acquirente, acquistabile: Acquistabile, nuovaQuantita: int): void
    +calcolaTotaleCarrello(acquirente: Acquirente): double
    +svuotaCarrello(acquirente: Acquirente): void
    +getRepository(): CarrelloRepository
}

class CertificazioneService {
    -certificazioneRepository: ICertificazioneRepository
    +CertificazioneService(certificazioneRepository: ICertificazioneRepository)
    +creaCertificazione(nome: String, descrizione: String, enteEmettitore: String, dataScadenza: Date): Certificazione
    +verificaCertificazione(certificazione: Certificazione): boolean
    +getAllCertificazioni(): List<Certificazione>
    +getCertificazioneById(id: Long): Optional<Certificazione>
    +eliminaCertificazione(id: Long): void
}

class CuratoreService {
    -venditoreRepository: IVenditoreRepository
    -prodottoRepository: IProdottoRepository
    -datiAziendaRepository: IDatiAziendaRepository
    -codaRevisioneProdotti: Queue<Prodotto>
    +CuratoreService(venditoreRepository: IVenditoreRepository, prodottoRepository: IProdottoRepository, datiAziendaRepository: IDatiAziendaRepository)
    +CuratoreService()
    +verificaProdotto(prodotto: Prodotto, approvato: boolean, feedback: String): void
    +verificaDatiAzienda(datiAzienda: DatiAzienda, approvato: boolean): void
    +aggiungiProdottoAllaCoda(prodotto: Prodotto): void
    +ottieniProssimoProdottoDaRevisione(): Optional<Prodotto>
    +ottieniProdottiInAttesaDiRevisione(): List<Prodotto>
    +ottieniDatiAziendaInAttesaDiRevisione(): List<DatiAzienda>
}

class CuratoreObserverService implements ICuratoreObserver {
    -curatoreService: CuratoreService
    +CuratoreObserverService(curatoreService: CuratoreService)
    +notificaNuovoProdotto(prodotto: Prodotto): void
}

class EventoService {
    -eventoRepository: IEventoRepository
    +EventoService(eventoRepository: IEventoRepository)
    +EventoService()
    +creaEvento(nomeEvento: String, descrizione: String, dataOraInizio: Date, dataOraFine: Date, luogoEvento: String, capienzaMassima: int, organizzatore: AnimatoreDellaFiliera): void
    +aggiornaEvento(idEvento: Long, nuovoNomeEvento: String, nuovaDescrizione: String, nuovaDataOraInizio: Date, nuovaDataOraFine: Date, nuovoLuogoEvento: String, nuovaCapienzaMassima: int, organizzatore: AnimatoreDellaFiliera): void
    +eliminaEvento(idEvento: Long, organizzatore: AnimatoreDellaFiliera): void
    +aggiungiAziendaPartecipante(idEvento: Long, venditore: Venditore): void
    +rimuoviAziendaPartecipante(idEvento: Long, venditore: Venditore): void
    +prenotaPosti(evento: Evento, quantita: int): void
    +eliminaPostiPrenotati(evento: Evento, quantita: int): void
    +iniziaEvento(idEvento: Long, organizzatore: AnimatoreDellaFiliera): void
    +terminaEvento(idEvento: Long, organizzatore: AnimatoreDellaFiliera): void
    +ottieniEventiPerOrganizzatore(organizzatore: AnimatoreDellaFiliera): List<Evento>
    +ottieniTuttiGliEventi(): List<Evento>
    +ottieniEventoPerNome(nome: String): List<Evento>
}

class GestoreService {
    -utenteBaseRepository: UtenteBaseRepository
    -venditoreRepository: VenditoreRepository
    -curatoreRepository: CuratoreRepository
    -animatoreRepository: AnimatoreRepository
    -datiAziendaRepository: DatiAziendaRepository
    +GestoreService(utenteBaseRepository: UtenteBaseRepository, venditoreRepository: VenditoreRepository, curatoreRepository: CuratoreRepository, animatoreRepository: AnimatoreRepository, datiAziendaRepository: DatiAziendaRepository)
    +getVenditoriInAttesaDiAccreditamento(): List<Venditore>
    +getCuratoriInAttesaDiAccreditamento(): List<Curatore>
    +getAnimatoriInAttesaDiAccreditamento(): List<AnimatoreDellaFiliera>
    +aggiornaStatoAccreditamentoVenditore(venditoreId: Long, nuovoStato: StatoAccreditamento): boolean
    +aggiornaStatoAccreditamentoCuratore(curatoreId: Long, nuovoStato: StatoAccreditamento): boolean
    +aggiornaStatoAccreditamentoAnimatore(animatoreId: Long, nuovoStato: StatoAccreditamento): boolean
    +attivaDisattivaAcquirente(acquirenteId: Long, attivo: boolean): boolean
    +attivaDisattivaVenditore(venditoreId: Long, attivo: boolean): boolean
    +attivaDisattivaCuratore(curatoreId: Long, attivo: boolean): boolean
    +attivaDisattivaAnimatore(animatoreId: Long, attivo: boolean): boolean
}

class OrdineService implements IOrdineObservable {
    -ordineRepository: OrdineRepository
    -rigaOrdineRepository: RigaOrdineRepository
    -carrelloService: CarrelloService
    -observers: List<IVenditoreObserver>
    +OrdineService()
    +OrdineService(carrelloService: CarrelloService)
    +creaNuovoOrdine(acquirente: Acquirente): void
    +calcolaPrezzoOrdine(ordine: Ordine): void
    +creaOrdineDaCarrello(acquirente: Acquirente): Ordine
    +confermaOrdine(ordine: Ordine): void
    +annullaOrdine(ordine: Ordine): void
    +cambiaStatoOrdine(ordine: Ordine): void
    +getOrdiniByAcquirente(acquirente: Acquirente): List<Ordine>
    +getOrdiniByStato(stato: StatoCorrente): List<Ordine>
    +getOrdiniByVenditore(venditore: Venditore): List<Ordine>
    +pagaOrdine(ordine: Ordine, metodoPagamento: IMetodoPagamentoStrategy): boolean
    +aggiungiObserver(observer: IVenditoreObserver): void
    +rimuoviObserver(observer: IVenditoreObserver): void
    +notificaObservers(ordine: Ordine): void
    +getOrdineRepository(): OrdineRepository
}

class PacchettoService {
    -pacchettoRepository: IPacchettoRepository
    -venditoreRepository: IVenditoreRepository
    +PacchettoService(pacchettoRepository: IPacchettoRepository, venditoreRepository: IVenditoreRepository)
    +PacchettoService()
    +creaPacchetto(nome: String, descrizione: String, quantita: int, prezzoPacchetto: double, distributore: DistributoreDiTipicita): Pacchetto
    +aggiornaPacchetto(pacchetto: Pacchetto): Pacchetto
    +eliminaPacchetto(idPacchetto: Long): void
    +aggiungiElementoAPacchetto(pacchetto: Pacchetto, elemento: Acquistabile): void
    +rimuoviElementoDaPacchetto(pacchetto: Pacchetto, elemento: Acquistabile): void
    +calcolaPrezzoAutomatico(pacchetto: Pacchetto): double
    +mostraTuttiIPacchetti(): List<Pacchetto>
    +trovaPacchettoPerNome(nome: String): List<Pacchetto>
    +trovaPacchettiPerDistributore(distributore: DistributoreDiTipicita): List<Pacchetto>
    +getRepository(): IPacchettoRepository
}

class ProcessoTrasformazioneService {
    -processoRepository: IProcessoTrasformazioneRepository
    +ProcessoTrasformazioneService(processoRepository: IProcessoTrasformazioneRepository)
    +creaProcesso(nome: String, descrizione: String, trasformatore: Trasformatore, metodoProduzione: String): ProcessoTrasformazione
    +aggiornaProcesso(processo: ProcessoTrasformazione): ProcessoTrasformazione
    +aggiungiFaseAlProcesso(processoId: Long, fase: FaseLavorazione): ProcessoTrasformazione
    +rimuoviFaseDalProcesso(processoId: Long, fase: FaseLavorazione): ProcessoTrasformazione
    +ottieniProcessiPerTrasformatore(trasformatore: Trasformatore): List<ProcessoTrasformazione>
    +ottieniProcessoPerNome(nome: String): Optional<ProcessoTrasformazione>
    +eliminaProcesso(processoId: Long): void
    +validaProcesso(processo: ProcessoTrasformazione): boolean
}

class ProdottoService implements IProdottoObservable {
    -prodottoRepository: IProdottoRepository
    -certificazioneService: ICertificazioneService
    -venditoreRepository: IVenditoreRepository
    -observers: List<ICuratoreObserver>
    +ProdottoService(prodottoRepository: IProdottoRepository, certificazioneService: ICertificazioneService, venditoreRepository: IVenditoreRepository)
    +ProdottoService(prodottoRepository: IProdottoRepository)
    +creaProdotto(nome: String, descrizione: String, prezzo: double, quantitaDisponibile: int, venditore: Venditore): Prodotto
    +aggiornaProdotto(prodotto: Prodotto): Prodotto
    +eliminaProdotto(idProdotto: Long): void
    +aumentaQuantita(idProdotto: Long, quantita: int): void
    +diminuisciQuantita(idProdotto: Long, quantita: int): void
    +mostraTuttiIProdotti(): List<Prodotto>
    +trovaProdottoPerNome(nome: String): List<Prodotto>
    +trovaProdottiPerVenditore(venditore: Venditore): List<Prodotto>
    +aggiungiCertificazioneAProdotto(prodotto: Prodotto, certificazione: Certificazione): void
    +rimuoviCertificazioneDaProdotto(prodotto: Prodotto, certificazione: Certificazione): void
    +aggiungiObserver(observer: ICuratoreObserver): void
    +rimuoviObserver(observer: ICuratoreObserver): void
    +notificaObservers(prodotto: Prodotto): void
}

class ProduttoreService extends VenditoreService {
    +ProduttoreService(certificazioneService: ICertificazioneService, venditoreRepository: IVenditoreRepository, datiAziendaRepository: IDatiAziendaRepository)
}

class RigaOrdineService {
    -rigaOrdineRepository: RigaOrdineRepository
    +RigaOrdineService(rigaOrdineRepository: RigaOrdineRepository)
    +RigaOrdineService()
    +creaRigaOrdine(acquistabile: Acquistabile, quantita: int, prezzoUnitario: double): RigaOrdine
    +aggiornaQuantita(rigaOrdine: RigaOrdine, nuovaQuantita: int): void
    +calcolaSubtotale(rigaOrdine: RigaOrdine): double
    +getRighePerOrdine(ordine: Ordine): List<RigaOrdine>
    +getRighePerAcquistabile(acquistabile: Acquistabile): List<RigaOrdine>
    +eliminaRigaOrdine(idRiga: Long): void
}

class VenditoreService {
    -venditoreRepository: IVenditoreRepository
    -certificazioneService: ICertificazioneService
    -datiAziendaRepository: IDatiAziendaRepository
    +VenditoreService(certificazioneService: ICertificazioneService, venditoreRepository: IVenditoreRepository, datiAziendaRepository: IDatiAziendaRepository)
    +aggiornaDatiAzienda(venditore: Venditore, datiAggiornati: DatiAzienda): void
    +aggiungiDatiAzienda(venditore: Venditore, nomeAzienda: String, partitaIva: String, indirizzoAzienda: String, descrizioneAzienda: String, logoAzienda: String, sitoWebAzienda: String): DatiAzienda
    +aggiungiCertificazioneAzienda(venditore: Venditore, certificazione: Certificazione): void
    +rimuoviCertificazioneAzienda(venditore: Venditore, certificazione: Certificazione): void
}

class VenditoreObserverService implements IVenditoreObserver {
    -prodottoService: ProdottoService
    -pacchettoService: PacchettoService
    +VenditoreObserverService(prodottoService: ProdottoService, pacchettoService: PacchettoService)
    +notificaNuovoOrdine(ordine: Ordine): void
}

' === CLASSI MAPPER ===
class ProcessoMapper {
    +toDto(processo: ProcessoTrasformazione): ProcessoTrasformazioneDTO
    -toFaseDto(fase: FaseLavorazione): FaseLavorazioneDTO
}

' === CLASSI DTO ===
class ProcessoTrasformazioneDTO {
    -idProcesso: Long
    -nome: String
    -descrizione: String
    -metodoProduzione: String
    -dataInizioProcesso: Date
    -dataFineProcesso: Date
    -note: String
    -idTrasformatore: Long
    -nomeTrasformatore: String
    -cognomeTrasformatore: String
    -nomeAziendaTrasformatore: String
    -fasi: List<FaseLavorazioneDTO>
    +ProcessoTrasformazioneDTO(idProcesso: Long, nome: String, descrizione: String, metodoProduzione: String, dataInizioProcesso: Date, dataFineProcesso: Date, note: String, idTrasformatore: Long, nomeTrasformatore: String, cognomeTrasformatore: String, nomeAziendaTrasformatore: String, fasi: List<FaseLavorazioneDTO>)
}

class FaseLavorazioneDTO {
    -id: Long
    -nome: String
    -descrizione: String
    -ordineSequenza: int
    -temperaturaProcesso: double
    -tempoLavorazione: int
    -fontiMateriePrime: List<FonteMateriaPrima>
    +FaseLavorazioneDTO(id: Long, nome: String, descrizione: String, ordineSequenza: int, temperaturaProcesso: double, tempoLavorazione: int, fontiMateriePrime: List<FonteMateriaPrima>)
}

' === ECCEZIONI ===
class OrdineException extends Exception {
    +OrdineException(message: String)
    +OrdineException(message: String, cause: Throwable)
}

class CarrelloVuotoException extends RuntimeException {
    +CarrelloVuotoException(message: String)
}

class QuantitaNonDisponibileException extends RuntimeException {
    +QuantitaNonDisponibileException(message: String)
}

class QuantitaNonDisponibileAlCheckoutException extends RuntimeException {
    +QuantitaNonDisponibileAlCheckoutException(message: String)
}

class PagamentoException extends Exception {
    +PagamentoException(message: String)
    +PagamentoException(message: String, cause: Throwable)
}

' === RELAZIONI DI EREDITARIETÀ ===
Utente <|-- Acquirente
Utente <|-- Venditore
Utente <|-- Curatore
Utente <|-- AnimatoreDellaFiliera
Utente <|-- GestorePiattaforma

Venditore <|-- Produttore
Venditore <|-- DistributoreDiTipicita
Venditore <|-- Trasformatore

VenditoreService <|-- ProduttoreService

Exception <|-- OrdineException
Exception <|-- PagamentoException
RuntimeException <|-- CarrelloVuotoException
RuntimeException <|-- QuantitaNonDisponibileException
RuntimeException <|-- QuantitaNonDisponibileAlCheckoutException

' === IMPLEMENTAZIONI DI INTERFACCE ===
Acquistabile <|.. Prodotto
Acquistabile <|.. Pacchetto
Acquistabile <|.. Evento

ElementoVerificabile <|.. Prodotto

FonteMateriaPrima <|.. FonteInterna
FonteMateriaPrima <|.. FonteEsterna

IStatoOrdine <|.. StatoOrdineNuovoInAttesaDiPagamento
IStatoOrdine <|.. StatoOrdinePagatoProntoPerLavorazione
IStatoOrdine <|.. StatoOrdineInLavorazione
IStatoOrdine <|.. StatoOrdineSpedito
IStatoOrdine <|.. StatoOrdineConsegnato
IStatoOrdine <|.. StatoOrdineAnnullato

IMetodoPagamentoStrategy <|.. PagamentoCartaCreditoStrategy
IMetodoPagamentoStrategy <|.. PagamentoPayPalStrategy
IMetodoPagamentoStrategy <|.. PagamentoSimulatoStrategy

UtenteFactory <|.. SimpleUtenteFactory

IProdottoObservable <|.. ProdottoService
ICuratoreObserver <|.. CuratoreObserverService

IOrdineObservable <|.. OrdineService
IVenditoreObserver <|.. VenditoreObserverService

' === RELAZIONI DI ASSOCIAZIONE ===
Utente --> TipoRuolo

Venditore --> "0..1" DatiAzienda
Venditore --> "*" Prodotto
Venditore --> StatoAccreditamento

Curatore --> StatoAccreditamento
AnimatoreDellaFiliera --> StatoAccreditamento

DatiAzienda --> "*" Certificazione

Prodotto --> "*" Certificazione
Prodotto --> TipoOrigineProdotto
Prodotto --> StatoVerificaValori

Pacchetto --> "*" Acquistabile
Pacchetto --> DistributoreDiTipicita

Carrello --> Acquirente
Carrello --> "*" ElementoCarrello

ElementoCarrello --> Acquistabile

Ordine --> Acquirente
Ordine --> "*" RigaOrdine
Ordine --> IStatoOrdine

RigaOrdine --> Acquistabile

Evento --> AnimatoreDellaFiliera
Evento --> "*" Venditore
Evento --> StatoEventoValori

ProcessoTrasformazione --> Trasformatore
ProcessoTrasformazione --> "*" FaseLavorazione

FaseLavorazione --> "*" FonteMateriaPrima

FonteInterna --> Prodotto

Prodotto --> "0..1" MetodoDiColtivazione

' === RELAZIONI SERVICE-REPOSITORY ===
CarrelloService --> CarrelloRepository

CertificazioneService --> ICertificazioneRepository

CuratoreService --> IVenditoreRepository
CuratoreService --> IProdottoRepository
CuratoreService --> IDatiAziendaRepository

CuratoreObserverService --> CuratoreService

EventoService --> IEventoRepository

GestoreService --> UtenteBaseRepository
GestoreService --> VenditoreRepository
GestoreService --> CuratoreRepository
GestoreService --> AnimatoreRepository
GestoreService --> DatiAziendaRepository

OrdineService --> OrdineRepository
OrdineService --> RigaOrdineRepository
OrdineService --> CarrelloService

PacchettoService --> IPacchettoRepository
PacchettoService --> IVenditoreRepository

ProcessoTrasformazioneService --> IProcessoTrasformazioneRepository

ProdottoService --> IProdottoRepository
ProdottoService --> "0..1" ICertificazioneService
ProdottoService --> "0..1" IVenditoreRepository

RigaOrdineService --> RigaOrdineRepository

VenditoreService --> IVenditoreRepository
VenditoreService --> ICertificazioneService
VenditoreService --> IDatiAziendaRepository

VenditoreObserverService --> ProdottoService
VenditoreObserverService --> PacchettoService

' === RELAZIONI OBSERVER ===
ProdottoService --> "*" ICuratoreObserver
OrdineService --> "*" IVenditoreObserver

@enduml

```

## Note Implementative

Il diagramma rappresenta fedelmente la struttura del codice sorgente e include:

1. **Gerarchia degli Utenti**: Classe astratta `Utente` con sottoclassi concrete e la classe astratta `Venditore` con le sue specializzazioni.

2. **Pattern Implementati**:
   - **Observer Pattern**: Per notificare curatori sui nuovi prodotti e venditori sui nuovi ordini
   - **State Pattern**: Per gestire gli stati degli ordini
   - **Strategy Pattern**: Per i metodi di pagamento
   - **Factory Pattern**: Per la creazione degli utenti

3. **Interfacce Repository**: Solo le interfacce sono incluse nel diagramma, non le implementazioni concrete come specificato.

4. **Classi Service**: Solo le implementazioni concrete sono incluse, non le interfacce come specificato.

5. **Relazioni dettagliate**: Tutte le associazioni, composizioni, aggregazioni e implementazioni sono rappresentate con la corretta molteplicità.

6. **Attributi e Metodi**: Tutti i principali attributi e metodi sono specificati con la corretta visibilità (+, -, #, ~).

Il diagramma è compatibile con Visual Paradigm e utilizza solo costrutti standard di PlantUML per garantire l'importazione senza errori.
