@startuml


skinparam linetype ortho
skinparam packageStyle rectangle
skinparam class {
    BackgroundColor White
    BorderColor Black
    ArrowColor Black
}

' User Model Package
package "model.utenti" {
    
    enum TipoRuolo {
        +ACQUIRENTE
        +VENDITORE
        +PRODUTTORE
        +TRASFORMATORE
        +DISTRIBUTORE_DI_TIPICITA
        +CURATORE
        +ANIMATORE_DELLA_FILIERA
        +GESTORE_PIATTAFORMA
    }
    
    enum StatoAccreditamento {
        +PENDING
        +ACCREDITATO
        +SOSPESO
        +RIFIUTATO
    }
    
    abstract class Utente {
        -Long idUtente
        -String nome
        -String cognome
        -String email
        -String passwordHash
        -String numeroTelefono
        -TipoRuolo tipoRuolo
        -boolean isAttivo
        
        +Utente(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo)
        +String getNome()
        +void setNome(String nome)
        +String getCognome()
        +void setCognome(String cognome)
        +String getEmail()
        +void setEmail(String email)
        +String getPasswordHash()
        +void setPasswordHash(String passwordHash)
        +String getNumeroTelefono()
        +void setNumeroTelefono(String numeroTelefono)
        +TipoRuolo getTipoRuolo()
        +void setTipoRuolo(TipoRuolo tipoRuolo)
        +boolean modificaPassword(String nuovaPassword)
        +void disattivaAccount()
        +void riattivaAccount()
        +Long getId()
        +Long getIdUtente()
        +boolean isAttivo()
        +void setAttivo(boolean attivo)
        +void setIdUtente(Long idUtente)
        +void setId(Long id)
    }
    
    abstract class Venditore {
        -DatiAzienda datiAzienda
        -List<Prodotto> prodottiOfferti
        -StatoAccreditamento statoAccreditamento
        
        +Venditore(String nome, String cognome, String email, String passwordHash, String numeroTelefono, DatiAzienda datiAzienda, TipoRuolo tipoRuolo)
        +void stampaDatiAzienda()
        +DatiAzienda getDatiAzienda()
        +List<Prodotto> getProdottiOfferti()
        +boolean gestisciOrdineRicevuto()
        +void setDatiAzienda(DatiAzienda datiAzienda)
        +void aggiungiCertificazione(Certificazione certificazione)
        +void aggiungiProdottoOfferto(Prodotto prodotto)
        +StatoAccreditamento getStatoAccreditamento()
        +void setStatoAccreditamento(StatoAccreditamento statoAccreditamento)
    }
    
    class Acquirente {
        +Acquirente(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo)
    }
    
    class Produttore {
        +Produttore(String nome, String cognome, String email, String passwordHash, String numeroTelefono, DatiAzienda datiAzienda, TipoRuolo tipoRuolo)
    }
    
    class Trasformatore {
        +Trasformatore(String nome, String cognome, String email, String passwordHash, String numeroTelefono, DatiAzienda datiAzienda, TipoRuolo tipoRuolo)
        +List<Prodotto> getProdottiTrasformati()
        +List<Prodotto> getProdottiColtivati()
        +int contaProdottiTrasformati()
        +int contaProdottiColtivati()
        +boolean offreProdottiTrasformati()
        +boolean offreProdottiColtivati()
        +boolean isProduttoreMisto()
        +String getDescrizioneCapacita()
        +String toString()
    }
    
    class DistributoreDiTipicita {
        -List<Pacchetto> pacchettiOfferti
        
        +DistributoreDiTipicita(String nome, String cognome, String email, String passwordHash, String numeroTelefono, DatiAzienda datiAzienda, TipoRuolo tipoRuolo)
        +List<Pacchetto> getPacchettiOfferti()
    }
    
    class Curatore {
        -StatoAccreditamento statoAccreditamento
        
        +Curatore(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo)
        +StatoAccreditamento getStatoAccreditamento()
        +void setStatoAccreditamento(StatoAccreditamento statoAccreditamento)
    }
    
    class AnimatoreDellaFiliera {
        -StatoAccreditamento statoAccreditamento
        
        +AnimatoreDellaFiliera(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo)
        +StatoAccreditamento getStatoAccreditamento()
        +void setStatoAccreditamento(StatoAccreditamento statoAccreditamento)
    }
    
    class GestorePiattaforma {
        +GestorePiattaforma(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo)
    }
    
    class DatiAzienda {
        -Long idVenditore
        -String nomeAzienda
        -String partitaIva
        -String indirizzoAzienda
        -String descrizioneAzienda
        -String logoUrl
        -String sitoWebUrl
        -StatoVerificaValori statoVerifica
        -String feedbackVerificaContenuto
        -List<Certificazione> certificazioniAzienda
        
        +DatiAzienda(Long idVenditore, String nomeAzienda, String partitaIva, String indirizzoAzienda, String descrizioneAzienda, String logoUrl, String sitoWebUrl)
        +DatiAzienda()
        +Long getIdAzienda()
        +void setIdVenditore(Long idVenditore)
        +String getNomeAzienda()
        +void setNomeAzienda(String nomeAzienda)
        +String getPartitaIva()
        +void setPartitaIva(String partitaIva)
        +String getIndirizzoAzienda()
        +void setIndirizzoAzienda(String indirizzoAzienda)
        +String getDescrizioneAzienda()
        +void setDescrizioneAzienda(String descrizioneAzienda)
        +String getLogoUrl()
        +void setLogoUrl(String logoUrl)
        +String getSitoWebUrl()
        +void setSitoWebUrl(String sitoWebUrl)
        +List<Certificazione> getCertificazioniAzienda()
        +void aggiungiCertificazione(Certificazione certificazione)
        +Long getId()
        +String getFeedbackVerifica()
        +void setFeedbackVerifica(String feedbackVerifica)
        +void setStatoVerifica(StatoVerificaValori statoVerifica)
        +StatoVerificaValori getStatoVerifica()
    }
}

' Common Interfaces Package
package "model.common" {
    
    interface Acquistabile {
        +Long getId()
        +String getNome()
        +String getDescrizione()
        +double getPrezzo()
        +Venditore getVenditore()
    }
    
    interface ElementoVerificabile {
        +void setStatoVerifica(StatoVerificaValori statoVerifica)
        +StatoVerificaValori getStatoVerifica()
        +Long getId()
        +String getFeedbackVerifica()
        +void setFeedbackVerifica(String feedbackVerifica)
    }
    
    enum StatoVerificaValori {
        +IN_REVISIONE
        +APPROVATO
        +RESPINTO
    }
}

' Catalog Package
package "model.catalogo" {
    
    enum TipoOrigineProdotto {
        +COLTIVATO_ALLEVATO
        +COLTIVATO
        +ALLEVATO
        +TRASFORMATO
        -String descrizione
        
        +TipoOrigineProdotto(String descrizione)
        +String getDescrizione()
        +boolean isTrasformato()
        +boolean isMateriaPrima()
        +boolean isColtivato()
        +boolean isAllevato()
    }
    
    class Prodotto {
        -Long idProdotto
        -String nome
        -String descrizione
        -double prezzo
        -int quantitaDisponibile
        -StatoVerificaValori statoVerifica
        -String feedbackVerifica
        -Venditore venditore
        -List<Certificazione> certificazioniProdotto
        -TipoOrigineProdotto tipoOrigine
        -Long idProcessoTrasformazioneOriginario
        -Long idMetodoDiColtivazione
        
        +Prodotto(String nome, String descrizione, double prezzo, int quantitaDisponibile, Venditore venditore)
        +Prodotto(String nome, String descrizione, double prezzo, int quantitaDisponibile, Venditore venditore, Long idProcessoTrasformazioneOriginario)
        +Long getId()
        +void setIdProdotto(Long idProdotto)
        +String getNome()
        +String getDescrizione()
        +double getPrezzo()
        +int getQuantitaDisponibile()
        +void setStatoVerifica(StatoVerificaValori statoVerifica)
        +StatoVerificaValori getStatoVerifica()
        +String getFeedbackVerifica()
        +void setFeedbackVerifica(String feedbackVerifica)
        +Venditore getVenditore()
        +void setQuantitaDisponibile(int quantita)
        +TipoOrigineProdotto getTipoOrigine()
        +void setTipoOrigine(TipoOrigineProdotto tipoOrigine)
        +Long getIdProcessoTrasformazioneOriginario()
        +void setIdProcessoTrasformazioneOriginario(Long idProcessoTrasformazioneOriginario)
        +void setProcessoProduzione(ProcessoTrasformazione processo)
        +ProcessoTrasformazione getProcessoProduzione()
        +boolean isTrasformato()
        +boolean isColtivato()
        +Long getIdMetodoDiColtivazione()
        +void setIdMetodoDiColtivazione(Long idMetodoDiColtivazione)
        +String toString()
        +List<Certificazione> getCertificazioni()
        +void aggiungiCertificazione(Certificazione certificazione)
    }
    
    class Pacchetto {
        -Long idPacchetto
        -String nome
        -String descrizione
        -int quantitaDisponibile
        -double prezzoPacchetto
        -List<Acquistabile> elementiInclusi
        -DistributoreDiTipicita distributore
        
        +Pacchetto(DistributoreDiTipicita distributore, String nome, String descrizione, int quantita, double prezzoPacchetto)
        +Long getId()
        +void SetId(Long idPacchetto)
        +String getNome()
        +String getDescrizione()
        +double getPrezzo()
        +Venditore getVenditore()
        +void aggiungiElemento(Acquistabile elemento)
        +void rimuoviElemento(Acquistabile elemento)
        +List<Acquistabile> getElementiInclusi()
        +DistributoreDiTipicita getDistributore()
        +int getQuantitaDisponibile()
        +void setQuantitaDisponibile(int quantitaDisponibile)
        +String toString()
    }
    
    class Certificazione {
        -Long idCertificazione
        -String nomeCertificazione
        -String enteRilascio
        -Date dataRilascio
        -Date dataScadenza
        -Long idProdottoAssociato
        -Long idAziendaAssociata
        
        +Certificazione(String nomeCertificazione, String enteRilascio, Date dataRilascio, Date dataScadenza, long idProdottoAssociato)
        +Certificazione(String nomeCertificazione, String enteRilascio, Date dataRilascio, Date dataScadenza, long idAziendaAssociata, boolean isAzienda)
        +Long getIdCertificazione()
        +String getNomeCertificazione()
        +String getEnteRilascio()
        +Date getDataRilascio()
        +Date getDataScadenza()
        +Long getIdProdottoAssociato()
        +Long getIdAziendaAssociata()
        +void setNomeCertificazione(String nomeCertificazione)
        +void setEnteRilascio(String enteRilascio)
        +void setDataRilascio(Date dataRilascio)
        +void setDataScadenza(Date dataScadenza)
        +void SetIdCertificazione(Long idCertificazione)
        +void stampaCertificazione()
    }
}

' Carrello Package
package "model.carrello" {
    
    class Carrello {
        -Long idCarrello
        -Acquirente acquirente
        -List<ElementoCarrello> elementiCarrello
        -Date ultimaModifica
        
        +Carrello(Acquirente acquirente)
        +Long getIdCarrello()
        +void setIdCarrello(Long idCarrello)
        +Acquirente getAcquirente()
        +void setAcquirente(Acquirente acquirente)
        +List<ElementoCarrello> getElementiCarrello()
        +void setElementiCarrello(List<ElementoCarrello> elementiCarrello)
        +Date getUltimaModifica()
        +void setUltimaModifica(Date ultimaModifica)
        +void aggiungiElemento(ElementoCarrello elemento)
        +void rimuoviElemento(ElementoCarrello elemento)
        +void svuota()
        +double calcolaPrezzoTotale()
    }
    
    class ElementoCarrello {
        -Acquistabile acquistabile
        -int quantita
        -double prezzoUnitario
        
        +ElementoCarrello(Acquistabile acquistabile, int quantita)
        +Acquistabile getAcquistabile()
        +void setAcquistabile(Acquistabile acquistabile)
        +int getQuantita()
        +void setQuantita(int quantita)
        +double getPrezzoUnitario()
        +void setPrezzoUnitario(double prezzoUnitario)
        +double calcolaPrezzoTotale()
    }
}

' Ordine Package
package "model.ordine" {
    
    enum StatoCorrente {
        +ATTESA_PAGAMENTO
        +PAGATO
        +SPEDITO
        +CONSEGNATO
        +ANNULLATO
        +IN_LAVORAZIONE
        +PRONTO_PER_LAVORAZIONE
        +IN_ATTESA_DI_CONSEGNA
    }
    
    class Ordine {
        -Long idOrdine
        -Date dataOrdine
        -double importoTotale
        -Acquirente acquirente
        -List<RigaOrdine> righeOrdine
        -IStatoOrdine stato
        
        +Ordine(Date dataOrdine, Acquirente acquirente)
        +Long getIdOrdine()
        +void setIdOrdine(Long idOrdine)
        +double getImportoTotale()
        +void setImportoTotale(double importoTotale)
        +Date getDataOrdine()
        +void setDataOrdine(Date dataOrdine)
        +Acquirente getAcquirente()
        +void setAcquirente(Acquirente acquirente)
        +StatoCorrente getStatoOrdine()
        +IStatoOrdine getStato()
        +void setStato(IStatoOrdine nuovoStato)
        +List<RigaOrdine> getRigheOrdine()
        +void setRigheOrdine(List<RigaOrdine> righeOrdine)
        +void processa()
        +void spedisci()
        +void annulla()
        +void consegna()
        +void paga()
    }
    
    class RigaOrdine {
        -Long idRiga
        -Acquistabile acquistabile
        -int quantitaOrdinata
        -double prezzoUnitario
        
        +RigaOrdine(Acquistabile acquistabile, int quantitaOrdinata, double prezzoUnitario)
        +Long getIdRiga()
        +void setIdRiga(Long idRiga)
        +Acquistabile getAcquistabile()
        +void setAcquistabile(Acquistabile acquistabile)
        +int getQuantitaOrdinata()
        +void setQuantitaOrdinata(int quantitaOrdinata)
        +double getPrezzoUnitario()
        +void setPrezzoUnitario(double prezzoUnitario)
        +String toString()
    }
}

' State Order Package
package "model.ordine.stateOrdine" {
    
    interface IStatoOrdine {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
        +void cambiaStato(Ordine ordine, IStatoOrdine nuovoStato)
    }
    
    class StatoOrdineNuovoInAttesaDiPagamento {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
    
    class StatoOrdinePagatoProntoPerLavorazione {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
    
    class StatoOrdineInLavorazione {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
    
    class StatoOrdineSpedito {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
    
    class StatoOrdineConsegnato {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
    
    class StatoOrdineAnnullato {
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +StatoCorrente getStatoCorrente()
    }
}

' Eventi Package
package "model.eventi" {
    
    enum StatoEventoValori {
        +IN_PROGRAMMA
        +IN_CORSO
        +CONCLUSO
        +ANNULLATO
    }
    
    class Evento {
        -Long idEvento
        -String nomeEvento
        -String descrizione
        -Date DataOraInizio
        -Date DataOraFine
        -String luogoEvento
        -int capienzaMassima
        -int postiAttualmentePrenotati
        -StatoEventoValori statoEvento
        -AnimatoreDellaFiliera organizzatore
        -List<Venditore> aziendePartecipanti
        
        +Evento(String nomeEvento, String descrizione, Date DataOraInizio, Date DataOraFine, String luogoEvento, int capienzaMassima, AnimatoreDellaFiliera organizzatore)
        +Long getId()
        +void setId(Long idEvento)
        +String getNome()
        +void setNome(String nomeEvento)
        +String getDescrizione()
        +void setDescrizione(String descrizione)
        +Date getDataOraInizio()
        +void setDataOraInizio(Date DataOraInizio)
        +Date getDataOraFine()
        +void setDataOraFine(Date DataOraFine)
        +String getLuogoEvento()
        +void setLuogoEvento(String luogoEvento)
        +int getCapienzaMassima()
        +void setCapienzaMassima(int capienzaMassima)
        +int getPostiDisponibili()
        +AnimatoreDellaFiliera getOrganizzatore()
        +double getPrezzo()
        +Venditore getVenditore()
        +void addAziendaPartecipante(Venditore venditore)
        +void removeAziendaPartecipante(Venditore venditore)
        +List<Venditore> getAziendePartecipanti()
        +int getPostiAttualmentePrenotati()
        +void setPostiAttualmentePrenotati(int postiAttualmentePrenotati)
        +StatoEventoValori getStatoEvento()
        +void setStatoEvento(StatoEventoValori statoEvento)
    }
}

' Trasformazione Package
package "model.trasformazione" {
    
    interface FonteMateriaPrima {
        +String getDescrizione()
    }
    
    class FonteInterna {
        -Produttore produttore
        
        +FonteInterna(Produttore produttore)
        +Produttore getProduttore()
        +String getDescrizione()
        +boolean equals(Object obj)
        +int hashCode()
        +String toString()
    }
    
    class FonteEsterna {
        -String nomeFornitore
        
        +FonteEsterna(String nomeFornitore)
        +String getNomeFornitore()
        +String getDescrizione()
        +boolean equals(Object obj)
        +int hashCode()
        +String toString()
    }
    
    class ProcessoTrasformazione {
        -Long id
        -String nome
        -String descrizione
        -Trasformatore trasformatore
        -List<FaseLavorazione> fasiLavorazione
        -Prodotto prodottoFinale
        -String metodoProduzione
        -String note
        
        +ProcessoTrasformazione(String nome, String descrizione, Trasformatore trasformatore)
        +ProcessoTrasformazione(String nome, String descrizione, Trasformatore trasformatore, Prodotto prodottoFinale, String metodoProduzione)
        +Long getId()
        +String getNome()
        +String getDescrizione()
        +Trasformatore getTrasformatore()
        +List<FaseLavorazione> getFasiLavorazione()
        +List<FaseLavorazione> getFasi()
        +Trasformatore getResponsabile()
        +Prodotto getProdottoFinale()
        +String getMetodoProduzione()
        +String getNote()
        +void setId(Long id)
        +void setNome(String nome)
        +void setDescrizione(String descrizione)
        +void setTrasformatore(Trasformatore trasformatore)
        +void setProdottoFinale(Prodotto prodottoFinale)
        +void setMetodoProduzione(String metodoProduzione)
        +void setNote(String note)
        +void aggiungiFase(FaseLavorazione fase)
        +boolean rimuoviFase(FaseLavorazione fase)
        +int getNumeroFasi()
        +boolean isCompleto()
        +boolean equals(Object o)
        +int hashCode()
        +String toString()
    }
    
    class FaseLavorazione {
        -Long id
        -String nome
        -String descrizione
        -int ordineEsecuzione
        -String materiaPrimaUtilizzata
        -FonteMateriaPrima fonte
        -String note
        
        +FaseLavorazione(String nome, String descrizione, int ordineEsecuzione, String materiaPrimaUtilizzata, FonteMateriaPrima fonte)
        +Long getId()
        +String getNome()
        +String getDescrizione()
        +int getOrdineEsecuzione()
        +String getMateriaPrimaUtilizzata()
        +FonteMateriaPrima getFonte()
        +String getNote()
        +void setId(Long id)
        +void setNome(String nome)
        +void setDescrizione(String descrizione)
        +void setOrdineEsecuzione(int ordineEsecuzione)
        +void setMateriaPrimaUtilizzata(String materiaPrimaUtilizzata)
        +void setFonte(FonteMateriaPrima fonte)
        +void setNote(String note)
        +boolean equals(Object o)
        +int hashCode()
        +String toString()
    }
}

' Coltivazione Package
package "model.coltivazione" {
    
    class MetodoDiColtivazione {
        -Long id
        -String nome
        -String descrizioneDettagliata
        -String tecnicaPrincipale
        -String ambienteColtivazione
        
        +MetodoDiColtivazione(Long id, String nome, String descrizioneDettagliata, String tecnicaPrincipale, String ambienteColtivazione)
        +Long getId()
        +void setId(Long id)
        +String getNome()
        +void setNome(String nome)
        +String getDescrizioneDettagliata()
        +void setDescrizioneDettagliata(String descrizioneDettagliata)
        +String getTecnicaPrincipale()
        +void setTecnicaPrincipale(String tecnicaPrincipale)
        +String getAmbienteColtivazione()
        +void setAmbienteColtivazione(String ambienteColtivazione)
        +String toString()
    }
}

' Repository Interfaces Package
package "model.repository" {
    
    interface IUtenteBaseRepository {
        +Utente save(Utente utente)
        +Optional<Utente> findById(Long id)
        +Optional<Utente> findByEmail(String email)
        +List<Utente> findAll()
    }
    
    interface IProdottoRepository {
        +void save(Prodotto prodotto)
        +Prodotto findById(Long id)
        +List<Prodotto> mostraTuttiIProdotti()
        +void deleteById(Long id)
        +List<Prodotto> findByVenditore(Venditore venditore)
        +List<Prodotto> findByNome(String nome)
    }
    
    interface ICarrelloRepository {
        +void save(Carrello carrello)
        +Carrello findById(Long id)
        +Carrello findByAcquirente(Acquirente acquirente)
        +void deleteById(Long id)
    }
    
    interface IOrdineRepository {
        +void save(Ordine ordine)
        +Ordine findById(Long id)
        +List<Ordine> findByAcquirente(Acquirente acquirente)
        +List<Ordine> findAll()
        +void deleteById(Long id)
    }
    
    interface IRigaOrdineRepository {
        +void save(RigaOrdine rigaOrdine)
        +RigaOrdine findById(Long id)
        +List<RigaOrdine> findByOrdine(Ordine ordine)
        +void deleteById(Long id)
    }
    
    interface IEventoRepository {
        +void save(Evento evento)
        +Evento findById(Long id)
        +List<Evento> findAll()
        +void deleteById(Long id)
        +List<Evento> findByOrganizzatore(AnimatoreDellaFiliera organizzatore)
    }
    
    interface ICertificazioneRepository {
        +void save(Certificazione certificazione)
        +Certificazione findById(Long id)
        +List<Certificazione> findAll()
        +void deleteById(Long id)
        +List<Certificazione> findByProdotto(Long idProdotto)
        +List<Certificazione> findByAzienda(Long idAzienda)
    }
    
    interface IProcessoTrasformazioneRepository {
        +void save(ProcessoTrasformazione processo)
        +ProcessoTrasformazione findById(Long id)
        +List<ProcessoTrasformazione> findAll()
        +void deleteById(Long id)
        +List<ProcessoTrasformazione> findByTrasformatore(Trasformatore trasformatore)
    }
    
    interface IPacchettoRepository {
        +void save(Pacchetto pacchetto)
        +Pacchetto findById(Long id)
        +List<Pacchetto> findAll()
        +void deleteById(Long id)
        +List<Pacchetto> findByDistributore(DistributoreDiTipicita distributore)
    }
    
    interface IVenditoreRepository {
        +void save(Venditore venditore)
        +Venditore findById(Long id)
        +List<Venditore> findAll()
        +void deleteById(Long id)
    }
    
    interface IAnimatoreRepository {
        +void save(AnimatoreDellaFiliera animatore)
        +AnimatoreDellaFiliera findById(Long id)
        +List<AnimatoreDellaFiliera> findAll()
        +void deleteById(Long id)
    }
    
    interface ICuratoreRepository {
        +void save(Curatore curatore)
        +Curatore findById(Long id)
        +List<Curatore> findAll()
        +void deleteById(Long id)
    }
    
    interface IDatiAziendaRepository {
        +void save(DatiAzienda datiAzienda)
        +DatiAzienda findById(Long id)
        +List<DatiAzienda> findAll()
        +void deleteById(Long id)
    }
    
    interface IMetodoDiColtivazioneRepository {
        +void save(MetodoDiColtivazione metodo)
        +MetodoDiColtivazione findById(Long id)
        +List<MetodoDiColtivazione> findAll()
        +void deleteById(Long id)
    }
}

' Service Implementation Package
package "service.impl" {
    
    class ProdottoService {
        -IProdottoRepository prodottoRepository
        -ICertificazioneService certificazioneService
        -IVenditoreRepository venditoreRepository
        -List<ICuratoreObserver> observers
        
        +ProdottoService(IProdottoRepository prodottoRepository, ICertificazioneService certificazioneService, IVenditoreRepository venditoreRepository)
        +ProdottoService(IProdottoRepository prodottoRepository)
        +Prodotto creaProdotto(String nome, String descrizione, double prezzo, int quantitaDisponibile, Venditore venditore)
        +List<Prodotto> getProdottiOfferti(Venditore venditore)
        +void rimuoviProdottoCatalogo(Venditore venditore, Prodotto prodotto)
        +void aggiornaQuantitaProdotto(Venditore venditore, Prodotto prodotto, int nuovaQuantita)
        +void aggiungiQuantitaProdotto(Venditore venditore, Prodotto prodotto, int quantitaAggiunta)
        +void rimuoviQuantitaProdotto(Venditore venditore, Prodotto prodotto, int quantitaRimossa)
        +Certificazione aggiungiCertificazioneAProdotto(Prodotto prodotto, String nomeCertificazione, String enteRilascio, Date dataRilascio, Date dataScadenza)
        +void rimuoviCertificazioneDaProdotto(Prodotto prodotto, Long idCertificazione)
        +IProdottoRepository getProdottoRepository()
        +List<Certificazione> getCertificazioniDelProdotto(Prodotto prodotto)
        +void decrementaQuantita(Long idProdotto, int quantitaDaDecrementare)
        +Prodotto aggiungiProdottoTrasformato(String nome, String descrizione, double prezzo, int quantitaDisponibile, Venditore venditore, Long idProcessoTrasformazione)
        +void impostaProdottoComeTrasformato(Prodotto prodotto, Venditore venditore, Long idProcessoTrasformazione)
        +void aggiungiObserver(ICuratoreObserver observer)
        +void rimuoviObserver(ICuratoreObserver observer)
        +void notificaObservers(Prodotto prodotto)
    }
    
    class CarrelloService {
        -ICarrelloRepository carrelloRepository
        
        +CarrelloService(ICarrelloRepository carrelloRepository)
        +Carrello creaCarrello(Acquirente acquirente)
        +void aggiungiElementoCarrello(Carrello carrello, Acquistabile acquistabile, int quantita)
        +void rimuoviElementoCarrello(Carrello carrello, ElementoCarrello elemento)
        +void svuotaCarrello(Carrello carrello)
        +double calcolaTotaleCarrello(Carrello carrello)
        +Carrello getCarrelloByAcquirente(Acquirente acquirente)
    }
    
    class OrdineService {
        -IOrdineRepository ordineRepository
        -IRigaOrdineRepository rigaOrdineRepository
        -ICarrelloRepository carrelloRepository
        -IProdottoRepository prodottoRepository
        -List<IVenditoreObserver> observers
        
        +OrdineService(IOrdineRepository ordineRepository, IRigaOrdineRepository rigaOrdineRepository, ICarrelloRepository carrelloRepository, IProdottoRepository prodottoRepository)
        +Ordine creaOrdine(Carrello carrello)
        +void processaOrdine(Ordine ordine)
        +void spedisciOrdine(Ordine ordine)
        +void annullaOrdine(Ordine ordine)
        +void consegnaOrdine(Ordine ordine)
        +List<Ordine> getOrdiniByAcquirente(Acquirente acquirente)
        +void aggiungiObserver(IVenditoreObserver observer)
        +void rimuoviObserver(IVenditoreObserver observer)
        +void notificaObservers(Ordine ordine)
        +boolean pagaOrdine(Ordine ordine, IMetodoPagamentoStrategy metodoPagamento)
    }
    
    class CertificazioneService {
        -ICertificazioneRepository certificazioneRepository
        
        +CertificazioneService(ICertificazioneRepository certificazioneRepository)
        +Certificazione creaCertificazionePerProdotto(String nome, String ente, Date rilascio, Date scadenza, Prodotto prodotto)
        +Certificazione creaCertificazionePerAzienda(String nome, String ente, Date rilascio, Date scadenza, DatiAzienda azienda)
        +List<Certificazione> getCertificazioniProdotto(Long idProdotto)
        +List<Certificazione> getCertificazioniAzienda(Long idAzienda)
        +Certificazione getCertificazioneById(Long id)
        +boolean rimuoviCertificazione(Long idCertificazione, Prodotto prodotto)
        +void rimuoviCertificazioneGlobale(Long idCertificazione)
    }
    
    class EventoService {
        -IEventoRepository eventoRepository
        
        +EventoService(IEventoRepository eventoRepository)
        +Evento creaEvento(String nome, String descrizione, Date inizio, Date fine, String luogo, int capienza, AnimatoreDellaFiliera organizzatore)
        +void aggiungiPartecipante(Evento evento, Venditore venditore)
        +void rimuoviPartecipante(Evento evento, Venditore venditore)
        +List<Evento> getEventiByOrganizzatore(AnimatoreDellaFiliera organizzatore)
        +List<Evento> getTuttiGliEventi()
    }
    
    class ProcessoTrasformazioneService {
        -IProcessoTrasformazioneRepository processoRepository
        
        +ProcessoTrasformazioneService(IProcessoTrasformazioneRepository processoRepository)
        +ProcessoTrasformazione creaProcesso(String nome, String descrizione, Trasformatore trasformatore)
        +void aggiungiFaseAlProcesso(ProcessoTrasformazione processo, FaseLavorazione fase)
        +boolean rimuoviFaseDalProcesso(ProcessoTrasformazione processo, FaseLavorazione fase)
        +List<ProcessoTrasformazione> getProcessiByTrasformatore(Trasformatore trasformatore)
        +ProcessoTrasformazione getProcessoById(Long id)
    }
    
    class PacchettoService {
        -IPacchettoRepository pacchettoRepository
        
        +PacchettoService(IPacchettoRepository pacchettoRepository)
        +Pacchetto creaPacchetto(DistributoreDiTipicita distributore, String nome, String descrizione, int quantita, double prezzo)
        +void aggiungiElementoAPacchetto(Pacchetto pacchetto, Acquistabile elemento)
        +void rimuoviElementoDaPacchetto(Pacchetto pacchetto, Acquistabile elemento)
        +List<Pacchetto> getPacchettiByDistributore(DistributoreDiTipicita distributore)
    }
    
    class VenditoreService {
        -IVenditoreRepository venditoreRepository
        
        +VenditoreService(IVenditoreRepository venditoreRepository)
        +void salvaVenditore(Venditore venditore)
        +Venditore getVenditoreById(Long id)
        +List<Venditore> getTuttiIVenditori()
        +void aggiornaStatoAccreditamento(Venditore venditore, StatoAccreditamento nuovoStato)
    }
    
    class CuratoreService {
        -ICuratoreRepository curatoreRepository
        
        +CuratoreService(ICuratoreRepository curatoreRepository)
        +void salvaCartore(Curatore curatore)
        +Curatore getCuratoreById(Long id)
        +List<Curatore> getTuttiICuratori()
        +void approvaElemento(ElementoVerificabile elemento, String feedback)
        +void respingiElemento(ElementoVerificabile elemento, String feedback)
    }
    
    class GestoreService {
        -IUtenteBaseRepository utenteRepository
        
        +GestoreService(IUtenteBaseRepository utenteRepository)
        +void approvaAccreditamento(Utente utente)
        +void rifiutaAccreditamento(Utente utente)
        +void sospendiAccreditamento(Utente utente)
        +List<Utente> getUtentiInAttesaDiAccreditamento()
    }
    
    class ProduttoreService {
        -IVenditoreRepository venditoreRepository
        -IMetodoDiColtivazioneRepository metodiRepository
        
        +ProduttoreService(IVenditoreRepository venditoreRepository, IMetodoDiColtivazioneRepository metodiRepository)
        +void salvaProduttore(Produttore produttore)
        +Produttore getProduttoreById(Long id)
        +void associaMetodoDiColtivazione(Prodotto prodotto, MetodoDiColtivazione metodo)
        +List<MetodoDiColtivazione> getMetodiDisponibili()
    }
    
    class RigaOrdineService {
        -IRigaOrdineRepository rigaOrdineRepository
        
        +RigaOrdineService(IRigaOrdineRepository rigaOrdineRepository)
        +RigaOrdine creaRigaOrdine(Acquistabile acquistabile, int quantita, double prezzo)
        +List<RigaOrdine> getRigheByOrdine(Ordine ordine)
        +void salvaRigaOrdine(RigaOrdine riga)
    }
    
    class CuratoreObserverService {
        -ICuratoreRepository curatoreRepository
        -List<ElementoVerificabile> codaRevisione
        
        +CuratoreObserverService(ICuratoreRepository curatoreRepository)
        +void onProdottoCreato(Prodotto prodotto)
        +List<ElementoVerificabile> getCodaRevisione()
        +void rimuoviDallaCoda(ElementoVerificabile elemento)
    }
    
    class VenditoreObserverService {
        -IVenditoreRepository venditoreRepository
        
        +VenditoreObserverService(IVenditoreRepository venditoreRepository)
        +void onOrdineCreato(Ordine ordine)
        +void notificaVenditore(Venditore venditore, String messaggio)
    }
}

' Factory Package
package "service.factory" {
    
    interface UtenteFactory {
        +Utente creaUtente(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo, boolean isAttivo, DatiAzienda datiAzienda, List<Prodotto> prodottiOfferti)
    }
    
    class SimpleUtenteFactory {
        +Utente creaUtente(String nome, String cognome, String email, String passwordHash, String numeroTelefono, TipoRuolo tipoRuolo, boolean isAttivo, DatiAzienda datiAzienda, List<Prodotto> prodottiOfferti)
    }
}

' Observer Package
package "service.observer" {
    
    interface IProdottoObservable {
        +void aggiungiObserver(ICuratoreObserver observer)
        +void rimuoviObserver(ICuratoreObserver observer)
        +void notificaObservers(Prodotto prodotto)
    }
    
    interface ICuratoreObserver {
        +void onProdottoCreato(Prodotto prodotto)
    }
    
    interface IOrdineObservable {
        +void aggiungiObserver(IVenditoreObserver observer)
        +void rimuoviObserver(IVenditoreObserver observer)
        +void notificaObservers(Ordine ordine)
    }
    
    interface IVenditoreObserver {
        +void onOrdineCreato(Ordine ordine)
    }
}

' Strategy Package
package "service.pagamento" {
    
    interface IMetodoPagamentoStrategy {
        +boolean elaboraPagamento(Ordine ordine)
    }
    
    class PagamentoCartaCreditoStrategy {
        +boolean elaboraPagamento(Ordine ordine)
    }
    
    class PagamentoPayPalStrategy {
        +boolean elaboraPagamento(Ordine ordine)
    }
    
    class PagamentoSimulatoStrategy {
        +boolean elaboraPagamento(Ordine ordine)
    }
    
    class PagamentoException {
        +PagamentoException(String message)
        +PagamentoException(String message, Throwable cause)
    }
}

' DTO Package
package "dto.processo" {
    
    class ProcessoTrasformazioneDTO {
        -Long idProcesso
        -String nomeProcesso
        -String descrizioneProcesso
        -String noteTecniche
        -Long idTrasformatore
        -String nomeTrasformatore
        -String cognomeTrasformatore
        -String aziendaTrasformatore
        -List<FaseLavorazioneDTO> fasi
        
        +ProcessoTrasformazioneDTO(Long idProcesso, String nomeProcesso, String descrizioneProcesso, LocalDateTime dataInizioProcesso, LocalDateTime dataFineProcesso, String noteTecniche, Long idTrasformatore, String nomeTrasformatore, String cognomeTrasformatore, String aziendaTrasformatore, List<FaseLavorazioneDTO> fasi)
        +long getIdProcesso()
        +String getNomeProcesso()
        +String getDescrizioneProcesso()
        +String getNoteTecniche()
        +Long getIdTrasformatore()
        +String getNomeTrasformatore()
        +String getCognomeTrasformatore()
        +String getAziendaTrasformatore()
        +List<FaseLavorazioneDTO> getFasi()
    }
    
    class FaseLavorazioneDTO {
        -Long idFase
        -String nomeFase
        -String descrizione
        -int ordineEsecuzione
        -String materiaPrima
        -String fonte
        
        +FaseLavorazioneDTO(Long idFase, String nomeFase, String descrizione, int ordineEsecuzione, String materiaPrima, String fonte)
        +Long getIdFase()
        +String getNomeFase()
        +String getDescrizione()
        +int getOrdineEsecuzione()
        +String getMateriaPrima()
        +String getFonte()
    }
}

' Exception Package
package "exception" {
    
    class OrdineException {
        +OrdineException(String message)
        +OrdineException(String message, Throwable cause)
        +OrdineException(Throwable cause)
    }
    
    class CarrelloVuotoException {
        +CarrelloVuotoException(String message)
    }
    
    class QuantitaNonDisponibileException {
        -Long idElemento
        -int quantitaRichiesta
        -int quantitaDisponibile
        -String tipoElemento
        
        +QuantitaNonDisponibileException(Long idElemento, int quantitaRichiesta, int quantitaDisponibile, String tipoElemento)
        +Long getIdElemento()
        +int getQuantitaRichiesta()
        +int getQuantitaDisponibile()
        +String getTipoElemento()
    }
    
    class QuantitaNonDisponibileAlCheckoutException {
        +QuantitaNonDisponibileAlCheckoutException(String message)
    }
}

' Relationships - Inheritance
Utente <|-- Acquirente
Utente <|-- Venditore
Venditore <|-- Produttore
Venditore <|-- Trasformatore
Venditore <|-- DistributoreDiTipicita
Utente <|-- Curatore
Utente <|-- AnimatoreDellaFiliera
Utente <|-- GestorePiattaforma

' Interface implementations
Acquistabile <|.. Prodotto
Acquistabile <|.. Pacchetto
Acquistabile <|.. Evento
ElementoVerificabile <|.. Prodotto
ElementoVerificabile <|.. DatiAzienda
FonteMateriaPrima <|.. FonteInterna
FonteMateriaPrima <|.. FonteEsterna
IStatoOrdine <|.. StatoOrdineNuovoInAttesaDiPagamento
IStatoOrdine <|.. StatoOrdinePagatoProntoPerLavorazione
IStatoOrdine <|.. StatoOrdineInLavorazione
IStatoOrdine <|.. StatoOrdineSpedito
IStatoOrdine <|.. StatoOrdineConsegnato
IStatoOrdine <|.. StatoOrdineAnnullato
UtenteFactory <|.. SimpleUtenteFactory
IProdottoObservable <|.. ProdottoService
ICuratoreObserver <|.. CuratoreObserverService
IOrdineObservable <|.. OrdineService
IVenditoreObserver <|.. VenditoreObserverService
IMetodoPagamentoStrategy <|.. PagamentoCartaCreditoStrategy
IMetodoPagamentoStrategy <|.. PagamentoPayPalStrategy
IMetodoPagamentoStrategy <|.. PagamentoSimulatoStrategy

' Composition and Association relationships
Venditore *-- DatiAzienda : "ha"
DatiAzienda *-- "0..*" Certificazione : "possiede"
Venditore o-- "0..*" Prodotto : "offre"
DistributoreDiTipicita o-- "0..*" Pacchetto : "crea"
Pacchetto o-- "0..*" Acquistabile : "contiene"
Carrello *-- "1" Acquirente : "appartiene a"
Carrello o-- "0..*" ElementoCarrello : "contiene"
ElementoCarrello --> "1" Acquistabile : "riferisce"
Ordine *-- "1" Acquirente : "effettuato da"
Ordine o-- "1..*" RigaOrdine : "composto da"
Ordine --> "1" IStatoOrdine : "ha stato"
RigaOrdine --> "1" Acquistabile : "riferisce"
Evento --> "1" AnimatoreDellaFiliera : "organizzato da"
Evento o-- "0..*" Venditore : "partecipano"
ProcessoTrasformazione --> "1" Trasformatore : "gestito da"
ProcessoTrasformazione o-- "0..*" FaseLavorazione : "composto da"
ProcessoTrasformazione --> "0..1" Prodotto : "produce"
FaseLavorazione --> "1" FonteMateriaPrima : "utilizza"
FonteInterna --> "1" Produttore : "fornita da"
Prodotto --> "0..1" ProcessoTrasformazione : "derivato da"
Prodotto --> "0..1" MetodoDiColtivazione : "coltivato con"

' Service dependencies
ProdottoService --> IProdottoRepository : "usa"
ProdottoService --> IVenditoreRepository : "usa"
CarrelloService --> ICarrelloRepository : "usa"
OrdineService --> IOrdineRepository : "usa"
OrdineService --> IRigaOrdineRepository : "usa"
OrdineService --> ICarrelloRepository : "usa"
OrdineService --> IProdottoRepository : "usa"
CertificazioneService --> ICertificazioneRepository : "usa"
EventoService --> IEventoRepository : "usa"
ProcessoTrasformazioneService --> IProcessoTrasformazioneRepository : "usa"
PacchettoService --> IPacchettoRepository : "usa"
VenditoreService --> IVenditoreRepository : "usa"
CuratoreService --> ICuratoreRepository : "usa"
GestoreService --> IUtenteBaseRepository : "usa"
ProduttoreService --> IVenditoreRepository : "usa"
ProduttoreService --> IMetodoDiColtivazioneRepository : "usa"
RigaOrdineService --> IRigaOrdineRepository : "usa"
CuratoreObserverService --> ICuratoreRepository : "usa"
VenditoreObserverService --> IVenditoreRepository : "usa"

@enduml